# /Users/<USER>/Desktop/aus_job/job_seeker_cli/utils/platform_utils.py
# Platform detection utility: Identifies job platform sources from URLs
# This file detects LinkedIn, Seek and other recruitment platforms to enable mixed platform processing

from typing import List, Dict, Any
from urllib.parse import urlparse

def detect_platform(url: str) -> str:
    """
    Detect the platform type from a job URL
    
    Args:
        url: Job link URL
        
    Returns:
        Platform type: 'linkedin', 'seek', 'unknown'
    """
    if not url:
        return 'unknown'
    
    domain = urlparse(url).netloc.lower()
    
    if 'linkedin.com' in domain:
        return 'linkedin'
    elif 'seek.com' in domain:
        return 'seek'
    else:
        return 'unknown'

def analyze_job_file(jobs: List[Dict[str, Any]]) -> Dict[str, int]:
    """
    Analyze platform distribution in job file
    
    Args:
        jobs: List of job dictionaries
        
    Returns:
        Platform job count statistics
    """
    platforms = {'linkedin': 0, 'seek': 0, 'unknown': 0}
    
    for job in jobs:
        url = job.get('recommendedJobLink', '')
        platform = detect_platform(url)
        platforms[platform] += 1
    
    return platforms

def get_platform_emoji(platform: str) -> str:
    """Get emoji icon for platform"""
    emoji_map = {
        'linkedin': '🔗',
        'seek': '🔍', 
        'unknown': '❓'
    }
    return emoji_map.get(platform, '❓')
