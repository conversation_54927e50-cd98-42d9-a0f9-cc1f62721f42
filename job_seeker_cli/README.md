# 🎯 Job Seeker CLI

一个命令行的自动化求职工作流工具,旨在简化和加速求职过程。它提供了一个交互式菜单,引导用户完成从职位抓取、AI 分析到求职信生成的全过程。

## 🚀 快速开始

### 1. 环境设置

首先,请确保您已安装 Python 3.8 或更高版本。

### 2. 安装依赖

克隆本仓库,并在项目根目录下运行以下命令以安装所需依赖:

```bash
pip install -r requirements.txt
```

### 3. 配置 API 密钥

为了使用 AI 分析功能,您需要一个 DeepSeek API 密钥。请在项目根目录下创建一个 `.env` 文件,并添加以下内容:

```
DEEPSEEK_API_KEY="您的_DEEPSEEK_API_密钥"
```

### 4. 准备数据

1.  将您的简历(Markdown 格式)放入 `data/personal/` 目录。
2.  将包含职位链接的 JSON 文件放入 `data/input/` 目录。JSON 文件应遵循以下格式:

    ```json
    [
      {
        "jobTitle": "Software Developer",
        "jobAdvertiser": "Tech Company Inc.",
        "recommendedJobLink": "https://example.com/job/123"
      }
    ]
    ```

### 5. 运行程序

通过以下命令启动交互式菜单:

```bash
python menu_system.py
```

程序将引导您完成后续操作。

## 核心功能

*   **职位抓取**: 从输入的 JSON 文件中读取职位链接,自动抓取详细的职位描述,并保存为新的 JSON 文件。
*   **AI 职位分析**: 利用 DeepSeek API,将抓取到的职位详情与您的简历进行比较,分析匹配度,并筛选出最相关的目标职位。
*   **求职信生成**: 针对筛选出的目标职位,结合您的简历和预设的模板,自动生成个性化的求职信。
*   **交互式菜单**: 提供一个用户友好的命令行菜单,引导用户完成各项操作,无需记忆复杂命令。
*   **模块化工作流**: 将不同的求职任务(抓取、分析、生成)分解为独立的工作流,易于维护和扩展。
*   **集中式服务**: 提供文件、路径和 API 管理服务,确保代码的整洁和一致性。

## 系统架构

本项目采用模块化、服务驱动的架构,主要由以下几个部分组成:

*   **表示层 (UI)**: `ui/` 目录负责用户交互和界面渲染,使用 `rich` 库构建美观的命令行界面。
*   **业务逻辑层 (Workflows)**: `workflows/` 目录定义了核心业务流程,如职位抓取、分析和求职信生成。
*   **服务层 (Services)**: `services/` 目录提供可重用的底层服务,如文件操作、路径管理和 API 请求。
*   **数据处理层 (Scripts)**: `scripts/` 目录包含执行具体数据处理任务的脚本,被工作流调用。
*   **启动与配置**: `menu_system.py` 是应用的入口,负责初始化和调度。

## 目录结构

```
job_seeker_cli/
├── config/              # 配置文件目录 (未来使用)
├── data/                # 数据存储目录
│   ├── input/           # 存放原始职位列表 (JSON)
│   ├── processed/       # 存放已抓取详情的职位 (JSON)
│   ├── target_jobs/     # 存放 AI 分析后的目标职位 (JSON)
│   └── personal/        # 存放个人信息 (简历、模板等)
├── doc/                 # 项目文档
├── output/              # 输出目录
│   ├── cover_letters/   # 存放生成的求职信
│   └── reports/         # 存放分析报告
├── scripts/             # 存放具体功能实现的核心脚本
├── services/            # 提供底层服务的模块
├── tests/               # 测试代码
├── ui/                  # 用户界面和交互逻辑
├── workflows/           # 定义核心业务流程
├── menu_system.py       # 应用主入口和菜单系统
├── README.md            # 项目说明
└── requirements.txt     # Python 依赖
```

## 故障排除

*   **ImportError**: 如果遇到导入错误,请确保您已运行 `pip install -r requirements.txt`。
*   **API Key Not Found**: 请检查项目根目录下是否存在 `.env` 文件,并且其中正确设置了 `DEEPSEEK_API_KEY`。
*   **FileNotFoundError**: 请确保已按照“准备数据”部分的说明,将所需文件放置在正确的目录中。

## 许可证

本项目采用 MIT 许可证。
