# Sui <PERSON>

**Email:** <EMAIL>  
**Mobile:** +86-159-8817-1024  
**Portfolio:** jaysean1.github.io  
**Project:** qiansui.notion.site

## Summary

• 9 years of experience as a Technical Product Leader, expertise in AI and IoT solutions.  
• Led the merchant support service on Alibaba. The product sales reached 21 million RMB, providing services including smart logistics solutions to over 50 leading companies.  
• Designed smart cockpit solutions, the cloud applications have been installed on 200,000 in-vehicle devices.  
• Created over 110,000 job opportunities for people with disabilities.

## Work Experience

### KOL Manager - 200K Followers Account (Remote) | May 2021 - Present

Managed Social Media Account: Xiao Han Student Reports (Part-time)

• Data-Driven Content Optimization: Managed a structured content creation process, including analysis of platform trends, research on emerging fashion trends, ideation of original outfit concepts, coordination of scene settings, and development of compelling commercial narratives to maximize engagement and impact.  
• Business Development and Revenue Strategy: Drove strategic partnerships between the account and top-tier fashion brands, enhancing product promotion. Developed a diversified revenue model, including advertising revenue, platform incentives, and support for the growth of the personal clothing brand, Normwear.  
• Intelligent AI-Driven Social Media Operations: Leveraged generative LLMs to support end-to-end account operations and content production, including: automating email and information processing with n8n, streamlining business collaboration workflows; utilizing Stable Diffusion + LoRA for creative content previews; and conducting data analysis for content distribution optimization.

### Product Leader - In-Vehicle Interactive Cloud Application & Intelligent Voice Assistant, Banma Smart Cockpit Solutions Product Department (Alibaba Cloud), Shanghai, China | May 2021 - Oct 2023

• In-Vehicle Citylife Service Cloud Application: Covered 200,000 in-vehicle devices, with 10% average weekly active users rate. [promotion video] [demo video]  
• In-Vehicle AI Voice Assistant: Developed for LS6 engineering vehicles and exhibited at Chengdu International Auto Show. Features include office assistance and traffic violation identification.

### Product Leader - Taobao Platform Consulting Services Product, Taobao Consulting Product Department (Alibaba.inc), Hangzhou, China | Sept 2018 - May 2021

• Merchant Support Chatbot: Supported 20,000 daily conversations, 80% resolved by AI. [website]  
• AI-Hybrid Service Desk: Provided services to over 50 international enterprises, resolving the remaining 20% of customer inquiries through AI assistance, with product sales exceeding 21 million. [website]  
• Inclusive Product For Disabled: Provided employment opportunities for 110,000 disabled people, saved $2M/month in costs. [website]

### Product Expert - Logistics & After-Sales Product Department (Alibaba.inc), Hangzhou, China | Mar 2016 - Sept 2018

• Warehouse Video Intelligent Inspection Solution: Sold 50,000 units, saved $24M costs annually, filed technology patent. [demo video]  
• Logistics Service AI Plugin: Integrated the ERP systems, sending over 60,000 abnormal logistics reminders to merchants daily, reducing Alibaba consumers refund rates by 30%. [website]

### Product Manager - Transaction Security Product Department (Alibaba.inc), Hangzhou, China | Nov 2014 - Mar 2016

• Risk Transaction Prevention Product: Identified 1 million high-risk orders on average per month, with an automated processing rate reaching 20%.  
• VIP Service Product: Provided dedicated account managers for top 200 core merchants on Alibaba platform. [website]

## Patents

• Sui Qian, F. Xiao, W. Bin; Information processing method and device and electronic device; CN113327114A (Pending)  
An information processing method and a technology of terminal equipment, which are applied in the fields of information processing methods, devices and electronic equipment, and can solve the problems of high business processing pressure and the like.

## Selected Owned Products

### Car Intelligent Cockpit, Banma AliOS | Product Leader (Dec 2022 - Sep 2023)

• Visual Perception-Based Identification of Traffic Violations: Developed a combined software and hardware product: identify illegal lane change by RANSAC Algorithm; recorde traffic violations by vehicle-mounted camera; automatically generate violation reports for vehicle owners to submitted to transportation authorities at one click.  
• AI Voice Assistance for In-Vehicle Office Work: Provided a seamless cross-device in-transit working experience by integrating office software suites on smartphone, in-vehicle display and laptops. Realized intelligent generation of work information summaries, email replies, and to-do processing, etc., through voice control.

### AI-Hybrid Service Desk (Alibaba) | Product Expert (Dec 2019 - Sep 2021)

• Crowdsourcing Mode: Utilized a social crowdsourcing model to automatically allocate 200K daily manual consultations to the public, reducing enterprise costs by 2M monthly.  
• AI-based Intent Classification Algorithm: Categorized 200,000 daily manual consultations by RNN intent identification algorithm, accurately distributing them to appropriate skill groups with 90% precision.  
• Social Impact: Provided employment opportunities for over 110,000 people with disabilities.

### Warehouse Video Intelligent Inspection Solution (Alibaba) | Product Expert (May 2021 - May 2022)

• IoT Creative Solution: Identified potential issues with efficiency and safety in e-commerce merchant refund logistics. Utilized intelligent hardware barcode scanners combined with warehouse management to upgrade 10% return acceptance efficiency.  
• Rapid Prototype: Completed a functional prototype within 1 month. Led and coordinated 4 technical teams for joint debugging, fully validating the feasibility and efficacy of the idea.  
• Business Implementation: Launched a pilot program with leading retailer, Adidas, iterating IoT solutions. Sold over 50,000 units, 15,000,000 Yuan.

## Education

### Zhejiang University of Technology, Hangzhou, China | Master of Applied Economics (Sep 2012 - Apr 2015)
### Zhejiang University of Technology Zhijiang College, Hangzhou, China | Bachelor’s Degree in International Economics and Trade (Sep 2008 - Sep 2012)

## Publications

• Wang, Q.X, Qian, S, & Pang, Y(2017). The changing relationship between industrialization and urbanization in China under environmental constraints: An efficiency analysis perspective. Geographical Sciences, 37(1), 92-101.  
• Qian, S. (2014). Efficiency of China’s industrialization and urbanization under environmental constraints (Master’s thesis). Zhejiang University of Technology, China.  
• Wu, T. Qian, S. (2014). Factors influencing labor income percentage of GDP: Empirical evidence from Chinese provincial panel data. Modern Marketing (Late Edition), (06).  
• Qian, S. Wu, T. (2014). Mechanisms and impacts of industrialization driving urbanization in China. Modern Marketing (Late Edition), (06).