import os
import sys
from typing import Dict, Any

# 加载 .env 文件
def load_env_file():
    """加载 .env 文件中的环境变量"""
    env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
    if os.path.exists(env_path):
        with open(env_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    os.environ[key] = value

# 在导入其他模块之前加载环境变量
load_env_file()

# 添加当前目录和父目录到Python路径，以支持直接运行
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

try:
    # 首先尝试相对于当前目录的导入
    from services.path_manager import PathManager
    from services.file_service import FileService
    from services.api_client import ApiClient
    from ui.menu_renderer import MenuRenderer
    from ui.user_interaction import UserInteraction
    from workflows.job_scraping import JobScrapingWorkflow
    from workflows.job_analysis import JobAnalysisWorkflow
    from workflows.cover_letter import CoverLetterWorkflow
except ImportError:
    # 如果失败，尝试绝对导入
    from job_seeker_cli.services.path_manager import PathManager
    from job_seeker_cli.services.file_service import FileService
    from job_seeker_cli.services.api_client import ApiClient
    from job_seeker_cli.ui.menu_renderer import MenuRenderer
    from job_seeker_cli.ui.user_interaction import UserInteraction
    from job_seeker_cli.workflows.job_scraping import JobScrapingWorkflow
    from job_seeker_cli.workflows.job_analysis import JobAnalysisWorkflow
    from job_seeker_cli.workflows.cover_letter import CoverLetterWorkflow

class MenuSystem:
    def __init__(self):
        self.renderer = MenuRenderer()
        self.interaction = UserInteraction()
        
        # Initialize services
        self.path_manager = PathManager()
        self.file_service = FileService(self.path_manager)
        
        api_key = os.getenv('DEEPSEEK_API_KEY')
        if not api_key:
            self.renderer.display_message("DEEPSEEK_API_KEY not set. Some features will be disabled.", is_error=True)
            self.api_client = None
        else:
            self.api_client = ApiClient(api_key=api_key, api_url='https://api.deepseek.com/chat/completions')

        # Initialize workflows
        self.workflows: Dict[str, Any] = {
            "scrape": JobScrapingWorkflow(self.file_service, self.interaction),
            "analyze": JobAnalysisWorkflow(self.file_service, self.api_client, self.interaction) if self.api_client else None,
            "generate": CoverLetterWorkflow(self.file_service, self.api_client, self.interaction) if self.api_client else None,
        }

    def run(self):
        """Main loop for the menu system."""
        # Display welcome screen with new layout
        self.renderer.display_welcome()
        
        main_menu_options = {
            "🕷️ 抓取职位详情": "scrape",
            "🔍 分析职位匹配": "analyze", 
            "📝 生成求职信": "generate",
        }

        while True:
            try:
                # 使用新的交互式菜单
                choice = self.interaction.get_choice(main_menu_options)

                if choice is None:
                    # 确认退出
                    if self.interaction.get_confirmation("🚪 确定要退出吗？"):
                        self.renderer.display_message("👋 感谢使用 JOB SEEKER CLI！")
                        break
                    else:
                        continue

                if not self.workflows.get(choice):
                    self.renderer.display_message(f"'{choice}' 功能不可用。请检查您的API密钥。", is_error=True)
                    continue

                # 显示加载状态
                self.renderer.display_loading(f"正在启动 {choice} 工作流...")
                
                workflow = self.workflows[choice]
                workflow.run()
                
                # 操作完成后暂停
                self.interaction.get_confirmation("按回车键继续...")
                
            except KeyboardInterrupt:
                if self.interaction.get_confirmation("🚪 确定要退出吗？"):
                    self.renderer.display_message("👋 感谢使用 JOB SEEKER CLI！")
                    break
            except Exception as e:
                self.renderer.display_message(f"发生错误: {e}", is_error=True)
                self.interaction.get_confirmation("按回车键继续...")

if __name__ == "__main__":
    menu = MenuSystem()
    menu.run()
