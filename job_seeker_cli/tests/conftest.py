# job_seeker_cli/tests/conftest.py
# Shared pytest fixtures and configuration for Job Seeker CLI tests

import pytest
import json
import tempfile
from pathlib import Path
from typing import Dict, Any, List

from config import get_config_manager
from services.data_manager import DataManager


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_config():
    """Provide a sample configuration for testing."""
    return {
        "app": {
            "name": "Job Seeker CLI Test",
            "version": "0.1.0",
            "debug": False
        },
        "paths": {
            "job_data_dir": "./test_data",
            "output_dir": "./test_output",
            "resume_path": "./test_resume.txt"
        },
        "api": {
            "deepseek_api_key": "test_key_12345",
            "timeout": 30,
            "max_retries": 3
        },
        "defaults": {
            "match_threshold": 80,
            "default_subtasks": 5,
            "default_priority": "medium"
        }
    }


@pytest.fixture
def sample_job_data():
    """Provide sample job data for testing."""
    return [
        {
            "jobAdvertiser": "Tech Corp",
            "jobTitle": "Senior Software Engineer", 
            "recommendedJobLink": "https://example.com/job/1",
            "jobDetails": "We are looking for a senior software engineer with Python experience...",
            "jobLocation": "Sydney, NSW",
            "salary": "$120,000 - $150,000",
            "jobType": "Full-time",
            "postedDate": "2024-01-15"
        },
        {
            "jobAdvertiser": "StartupABC",
            "jobTitle": "Python Developer",
            "recommendedJobLink": "https://linkedin.com/job/2",
            "jobDetails": "Join our dynamic team as a Python developer. React experience preferred...",
            "jobLocation": "Melbourne, VIC", 
            "salary": "$90,000 - $110,000",
            "jobType": "Full-time",
            "postedDate": "2024-01-16"
        },
        {
            "jobAdvertiser": "Big Enterprise",
            "jobTitle": "Data Scientist",
            "recommendedJobLink": "https://seek.com.au/job/3", 
            "jobDetails": "We need a data scientist with machine learning expertise...",
            "jobLocation": "Brisbane, QLD",
            "salary": "$130,000 - $160,000",
            "jobType": "Full-time",
            "postedDate": "2024-01-17"
        }
    ]


@pytest.fixture
def sample_job_file(temp_dir, sample_job_data):
    """Create a sample job data file for testing."""
    job_file = temp_dir / "sample_jobs.json"
    with open(job_file, 'w', encoding='utf-8') as f:
        json.dump(sample_job_data, f, indent=2)
    return job_file


@pytest.fixture
def invalid_job_file(temp_dir):
    """Create an invalid job data file for testing."""
    job_file = temp_dir / "invalid_jobs.json"
    with open(job_file, 'w', encoding='utf-8') as f:
        f.write("{ invalid json content }")
    return job_file


@pytest.fixture
def empty_job_file(temp_dir):
    """Create an empty job data file for testing."""
    job_file = temp_dir / "empty_jobs.json"
    job_file.touch()
    return job_file


@pytest.fixture
def config_manager(temp_dir, sample_config):
    """Create a ConfigManager instance for testing."""
    config_file = temp_dir / "test_config.yaml"
    return get_config_manager(config_file_path=str(config_file), initial_config=sample_config)


@pytest.fixture
def data_manager(temp_dir):
    """Create a DataManager instance for testing."""
    return DataManager(str(temp_dir))


@pytest.fixture
def sample_resume_content():
    """Provide sample resume content for testing."""
    return """
    John Doe
    Senior Software Engineer
    
    EXPERIENCE:
    - 5+ years Python development
    - React and Django experience  
    - Machine learning projects
    - AWS cloud deployment
    
    SKILLS:
    - Python, JavaScript, SQL
    - React, Django, Flask
    - Docker, Kubernetes
    - Machine Learning, Data Science
    """


@pytest.fixture
def mock_api_response():
    """Provide a mock API response for testing."""
    return {
        "output": {
            "choices": [
                {
                    "message": {
                        "content": json.dumps({
                            "score": 85,
                            "evaluation": "Strong technical match with Python and React experience",
                            "strengths": "Python expertise, relevant frameworks",
                            "concerns": "Limited machine learning experience"
                        })
                    }
                }
            ]
        }
    } 