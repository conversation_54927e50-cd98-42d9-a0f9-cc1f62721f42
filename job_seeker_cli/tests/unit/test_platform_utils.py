# /Users/<USER>/Desktop/aus_job/job_seeker_cli/tests/unit/test_platform_utils.py
# Unit tests for platform detection utilities
# This file tests the platform detection functionality for LinkedIn, Seek and other platforms

import pytest
from job_seeker_cli.utils.platform_utils import detect_platform, analyze_job_file, get_platform_emoji

class TestPlatformUtils:
    """Test platform detection utilities"""
    
    def test_detect_linkedin_platform(self):
        """Test LinkedIn platform detection"""
        linkedin_urls = [
            "https://www.linkedin.com/jobs/view/*********",
            "https://au.linkedin.com/jobs/view/*********",
            "https://linkedin.com/in/company/job-posting"
        ]
        
        for url in linkedin_urls:
            assert detect_platform(url) == 'linkedin'
    
    def test_detect_seek_platform(self):
        """Test Seek platform detection"""
        seek_urls = [
            "https://www.seek.com.au/job/*********",
            "https://seek.com.au/jobs/software-engineer",
            "https://www.seek.com.au/career-advice"
        ]
        
        for url in seek_urls:
            assert detect_platform(url) == 'seek'
    
    def test_detect_unknown_platform(self):
        """Test unknown platform detection"""
        unknown_urls = [
            "https://www.indeed.com/jobs/view/123",
            "https://jobs.google.com/search",
            "https://example.com/careers",
            ""
        ]
        
        for url in unknown_urls:
            assert detect_platform(url) == 'unknown'
    
    def test_analyze_job_file(self):
        """Test job file platform analysis"""
        jobs = [
            {"recommendedJobLink": "https://www.linkedin.com/jobs/view/123"},
            {"recommendedJobLink": "https://www.seek.com.au/job/456"},
            {"recommendedJobLink": "https://www.linkedin.com/jobs/view/789"},
            {"recommendedJobLink": "https://example.com/job/999"},
            {"recommendedJobLink": ""}
        ]
        
        stats = analyze_job_file(jobs)
        
        assert stats['linkedin'] == 2
        assert stats['seek'] == 1
        assert stats['unknown'] == 2
    
    def test_get_platform_emoji(self):
        """Test platform emoji mapping"""
        assert get_platform_emoji('linkedin') == '🔗'
        assert get_platform_emoji('seek') == '🔍'
        assert get_platform_emoji('unknown') == '❓'
        assert get_platform_emoji('invalid') == '❓'
