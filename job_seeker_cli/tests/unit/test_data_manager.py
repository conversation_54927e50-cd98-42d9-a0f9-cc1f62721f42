# job_seeker_cli/tests/unit/test_data_manager.py
# Unit tests for data management module

import pytest
import json
from pathlib import Path
from unittest.mock import patch, MagicMock

from services.data_manager import DataManager, JobDataFile, JOB_SCHEMA


class TestJobDataFile:
    """Test cases for JobDataFile class."""
    
    def test_valid_job_file_loading(self, sample_job_file, sample_job_data):
        """Test loading a valid job data file."""
        job_file = JobDataFile(sample_job_file)
        
        assert job_file.is_valid is True
        assert job_file.job_count == len(sample_job_data)
        assert len(job_file.validation_errors) == 0
        assert job_file.status_icon == "✅"
        
        # Check that job data is loaded correctly
        assert len(job_file.job_data) == 3
        assert job_file.job_data[0]["jobAdvertiser"] == "Tech Corp"
    
    def test_invalid_job_file_loading(self, invalid_job_file):
        """Test loading an invalid job data file."""
        job_file = JobDataFile(invalid_job_file)
        
        assert job_file.is_valid is False
        assert job_file.job_count == 0
        assert len(job_file.validation_errors) > 0
        assert job_file.status_icon == "❌"
        assert "Invalid JSON" in job_file.validation_errors[0]
    
    def test_empty_job_file_loading(self, empty_job_file):
        """Test loading an empty job data file."""
        job_file = JobDataFile(empty_job_file)
        
        assert job_file.is_valid is False
        assert job_file.job_count == 0
        assert "File is empty" in job_file.validation_errors
    
    def test_job_file_size_formatting(self, sample_job_file):
        """Test file size formatting."""
        job_file = JobDataFile(sample_job_file)
        
        # Test that size is calculated
        assert job_file.size_bytes > 0
        assert job_file.size_mb > 0
        assert job_file.size_formatted.endswith(("B", "KB", "MB"))
    
    def test_job_file_schema_validation(self, temp_dir):
        """Test schema validation for job data."""
        # Create file with missing required fields
        incomplete_job = [{
            "jobAdvertiser": "Test Corp",
            # Missing jobTitle, recommendedJobLink, jobDetails
        }]
        
        job_file_path = temp_dir / "incomplete.json"
        with open(job_file_path, 'w') as f:
            json.dump(incomplete_job, f)
        
        job_file = JobDataFile(job_file_path)
        
        assert job_file.is_valid is False
        assert any("Missing required field" in error for error in job_file.validation_errors)
    
    def test_job_file_preview_data(self, sample_job_file):
        """Test getting preview data from job file."""
        job_file = JobDataFile(sample_job_file)
        preview = job_file.get_preview_data()
        
        assert "jobAdvertiser" in preview
        assert "jobTitle" in preview
        assert preview["jobAdvertiser"] == "Tech Corp"
        assert preview["jobTitle"] == "Senior Software Engineer"
    
    def test_single_job_object_validation(self, temp_dir, sample_job_data):
        """Test validation of single job object (not array)."""
        single_job_file = temp_dir / "single_job.json"
        with open(single_job_file, 'w') as f:
            json.dump(sample_job_data[0], f)  # Single job object
        
        job_file = JobDataFile(single_job_file)
        
        assert job_file.is_valid is True
        assert job_file.job_count == 1


class TestDataManager:
    """Test cases for DataManager class."""
    
    def test_data_manager_initialization(self, temp_dir):
        """Test DataManager initialization."""
        manager = DataManager(str(temp_dir))
        
        assert manager.data_directory == temp_dir
        assert manager.job_files == []
        assert manager.last_scan_time is None
    
    def test_scan_directory_with_valid_files(self, temp_dir, sample_job_data):
        """Test scanning directory with valid job files."""
        # Create multiple job files
        for i, job_data in enumerate([sample_job_data[:1], sample_job_data[1:2], sample_job_data[2:]]):
            job_file = temp_dir / f"jobs_{i}.json"
            with open(job_file, 'w') as f:
                json.dump(job_data, f)
        
        manager = DataManager(str(temp_dir))
        
        with patch('rich.console.Console.print'):  # Suppress console output
            file_count = manager.scan_directory(show_progress=False)
        
        assert file_count == 3
        assert len(manager.job_files) == 3
        assert manager.last_scan_time is not None
        
        # Check that all files are valid
        valid_files = [f for f in manager.job_files if f.is_valid]
        assert len(valid_files) == 3
    
    def test_scan_directory_with_mixed_files(self, temp_dir, sample_job_data):
        """Test scanning directory with both valid and invalid files."""
        # Create valid file
        valid_file = temp_dir / "valid_jobs.json"
        with open(valid_file, 'w') as f:
            json.dump(sample_job_data, f)
        
        # Create invalid file
        invalid_file = temp_dir / "invalid_jobs.json"
        with open(invalid_file, 'w') as f:
            f.write("{ invalid json }")
        
        # Create empty file
        empty_file = temp_dir / "empty_jobs.json"
        empty_file.touch()
        
        manager = DataManager(str(temp_dir))
        
        with patch('rich.console.Console.print'):
            file_count = manager.scan_directory(show_progress=False)
        
        assert file_count == 3
        assert len(manager.job_files) == 3
        
        valid_files = [f for f in manager.job_files if f.is_valid]
        invalid_files = [f for f in manager.job_files if not f.is_valid]
        
        assert len(valid_files) == 1
        assert len(invalid_files) == 2
    
    def test_scan_nonexistent_directory(self, temp_dir):
        """Test scanning a nonexistent directory."""
        nonexistent_dir = temp_dir / "nonexistent"
        manager = DataManager(str(nonexistent_dir))
        
        with patch('rich.console.Console.print'):
            file_count = manager.scan_directory(show_progress=False)
        
        assert file_count == 0
        assert len(manager.job_files) == 0
    
    def test_scan_directory_caching(self, temp_dir, sample_job_data):
        """Test that scan results are cached when not forced."""
        # Create a job file
        job_file = temp_dir / "jobs.json"
        with open(job_file, 'w') as f:
            json.dump(sample_job_data, f)
        
        manager = DataManager(str(temp_dir))
        
        # First scan
        with patch('rich.console.Console.print'):
            file_count1 = manager.scan_directory(show_progress=False)
        
        # Second scan (should use cache)
        with patch('rich.console.Console.print'):
            file_count2 = manager.scan_directory(force_rescan=False, show_progress=False)
        
        assert file_count1 == file_count2 == 1
        
        # Force rescan
        with patch('rich.console.Console.print'):
            file_count3 = manager.scan_directory(force_rescan=True, show_progress=False)
        
        assert file_count3 == 1
    
    def test_get_summary_stats(self, temp_dir, sample_job_data):
        """Test getting summary statistics."""
        # Create valid and invalid files
        valid_file = temp_dir / "valid_jobs.json"
        with open(valid_file, 'w') as f:
            json.dump(sample_job_data, f)
        
        invalid_file = temp_dir / "invalid_jobs.json"
        with open(invalid_file, 'w') as f:
            f.write("invalid json")
        
        manager = DataManager(str(temp_dir))
        
        with patch('rich.console.Console.print'):
            manager.scan_directory(show_progress=False)
        
        stats = manager.get_summary_stats()
        
        assert stats["total_files"] == 2
        assert stats["valid_files"] == 1
        assert stats["invalid_files"] == 1
        assert stats["total_jobs"] == 3  # sample_job_data has 3 jobs
        assert "total_size_formatted" in stats
        assert stats["avg_jobs_per_file"] == 3.0
    
    def test_get_summary_stats_empty(self, temp_dir):
        """Test getting summary statistics with no files."""
        manager = DataManager(str(temp_dir))
        stats = manager.get_summary_stats()
        
        assert stats == {}
    
    @patch('rich.console.Console.print')
    def test_display_files_table(self, mock_print, temp_dir, sample_job_data):
        """Test displaying files table."""
        # Create job file
        job_file = temp_dir / "jobs.json"
        with open(job_file, 'w') as f:
            json.dump(sample_job_data, f)
        
        manager = DataManager(str(temp_dir))
        manager.scan_directory(show_progress=False)
        
        # Test displaying table
        manager.display_files_table()
        
        # Should call print at least once
        assert mock_print.called
    
    @patch('rich.console.Console.print')
    def test_display_files_table_empty(self, mock_print, temp_dir):
        """Test displaying files table when no files exist."""
        manager = DataManager(str(temp_dir))
        
        manager.display_files_table()
        
        # Should print message about no files
        mock_print.assert_called()
        call_args = mock_print.call_args[0][0]
        assert "No job data files found" in call_args
    
    def test_get_valid_files(self, temp_dir, sample_job_data):
        """Test getting only valid files."""
        # Create valid and invalid files
        valid_file = temp_dir / "valid_jobs.json"
        with open(valid_file, 'w') as f:
            json.dump(sample_job_data, f)
        
        invalid_file = temp_dir / "invalid_jobs.json"
        with open(invalid_file, 'w') as f:
            f.write("invalid json")
        
        manager = DataManager(str(temp_dir))
        
        with patch('rich.console.Console.print'):
            manager.scan_directory(show_progress=False)
        
        valid_files = manager.get_valid_files()
        
        assert len(valid_files) == 1
        assert valid_files[0].is_valid is True
        assert valid_files[0].filename == "valid_jobs.json"


@pytest.mark.unit
class TestDataManagerIntegration:
    """Integration tests for DataManager functionality."""
    
    def test_full_data_workflow(self, temp_dir, sample_job_data):
        """Test complete data management workflow."""
        # Step 1: Create job data files
        job_files = []
        for i in range(3):
            job_file = temp_dir / f"batch_{i}.json"
            job_data = sample_job_data[i:i+1]  # One job per file
            with open(job_file, 'w') as f:
                json.dump(job_data, f)
            job_files.append(job_file)
        
        # Step 2: Initialize manager and scan
        manager = DataManager(str(temp_dir))
        
        with patch('rich.console.Console.print'):
            file_count = manager.scan_directory(show_progress=False)
        
        assert file_count == 3
        
        # Step 3: Get statistics
        stats = manager.get_summary_stats()
        assert stats["total_files"] == 3
        assert stats["valid_files"] == 3
        assert stats["total_jobs"] == 3
        
        # Step 4: Get valid files
        valid_files = manager.get_valid_files()
        assert len(valid_files) == 3
        
        # Step 5: Test file details
        for job_file in valid_files:
            assert job_file.is_valid
            assert job_file.job_count == 1
            preview = job_file.get_preview_data()
            assert "jobAdvertiser" in preview
            assert "jobTitle" in preview 