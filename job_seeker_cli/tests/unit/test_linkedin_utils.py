# /Users/<USER>/Desktop/aus_job/job_seeker_cli/tests/unit/test_linkedin_utils.py
# Unit tests for LinkedIn utilities
# This file tests the LinkedIn job scraping functionality including HTML cleaning and fetching

import pytest
from unittest.mock import Mock, patch
from job_seeker_cli.scripts.linkedin_utils import LinkedInFetcher, clean_html_text

class TestLinkedInUtils:
    """Test LinkedIn utilities"""
    
    def test_clean_html_text(self):
        """Test HTML text cleaning"""
        html_content = """
        <div>
            <p>This is a job description.</p>
            <ul>
                <li>Requirement 1</li>
                <li>Requirement 2</li>
            </ul>
            <!-- This is a comment -->
            <span>   Extra   spaces   </span>
        </div>
        """
        
        cleaned = clean_html_text(html_content)
        
        assert "This is a job description." in cleaned
        assert "Requirement 1" in cleaned
        assert "Requirement 2" in cleaned
        assert "Extra spaces" in cleaned
        assert "<!--" not in cleaned
        assert "  " not in cleaned  # No double spaces
    
    def test_linkedin_fetcher_initialization(self):
        """Test LinkedInFetcher initialization"""
        fetcher = LinkedInFetcher()
        
        assert fetcher.driver is None
        assert fetcher.logger is not None
    
    @patch('job_seeker_cli.scripts.linkedin_utils.requests.get')
    def test_fetch_with_requests_success(self, mock_get):
        """Test successful fetch with requests"""
        # Mock response
        mock_response = Mock()
        mock_response.text = '''
        <html>
            <body>
                <div class="mt4">
                    <p>Job description content</p>
                </div>
            </body>
        </html>
        '''
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        fetcher = LinkedInFetcher()
        result = fetcher._fetch_with_requests("https://linkedin.com/jobs/view/123")
        
        assert "Job description content" in result
        mock_get.assert_called_once()
    
    @patch('job_seeker_cli.scripts.linkedin_utils.requests.get')
    def test_fetch_with_requests_failure(self, mock_get):
        """Test fetch with requests failure"""
        mock_get.side_effect = Exception("Network error")
        
        fetcher = LinkedInFetcher()
        result = fetcher._fetch_with_requests("https://linkedin.com/jobs/view/123")
        
        assert result is None
    
    def test_cleanup(self):
        """Test cleanup method"""
        fetcher = LinkedInFetcher()
        # Should not raise any errors even with no driver
        fetcher.cleanup()
        
        # Test with mock driver
        mock_driver = Mock()
        fetcher.driver = mock_driver
        fetcher.cleanup()
        
        mock_driver.quit.assert_called_once()
    
    @patch('job_seeker_cli.scripts.linkedin_utils.time.sleep')
    @patch('job_seeker_cli.scripts.linkedin_utils.uniform')
    def test_add_random_delay(self, mock_uniform, mock_sleep):
        """Test random delay functionality"""
        mock_uniform.return_value = 5.0
        
        fetcher = LinkedInFetcher()
        fetcher.add_random_delay()
        
        mock_uniform.assert_called_once_with(3.0, 7.0)
        mock_sleep.assert_called_once_with(5.0)
