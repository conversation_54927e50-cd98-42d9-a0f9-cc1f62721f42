# job_seeker_cli/tests/unit/test_config.py
# Unit tests for configuration management module

import pytest
import yaml
from pathlib import Path
from unittest.mock import patch, mock_open

from config import ConfigManager, get_config_manager


class TestConfigManager:
    """Test cases for ConfigManager class."""
    
    def test_init_with_default_config(self, temp_dir, sample_config):
        """Test ConfigManager initialization with default configuration."""
        config_file = temp_dir / "test_config.yaml"
        manager = ConfigManager(str(config_file), sample_config)
        
        assert manager.config_file_path == str(config_file)
        assert manager.config == sample_config
        
    def test_get_nested_config_value(self, temp_dir, sample_config):
        """Test getting nested configuration values using dot notation."""
        config_file = temp_dir / "test_config.yaml"
        manager = ConfigManager(str(config_file), sample_config)
        
        # Test simple value
        assert manager.get("app.name") == "Job Seeker CLI Test"
        
        # Test nested value
        assert manager.get("api.deepseek_api_key") == "test_key_12345"
        assert manager.get("defaults.match_threshold") == 80
        
        # Test non-existent key
        assert manager.get("nonexistent.key") is None
        
        # Test with default value
        assert manager.get("nonexistent.key", "default_value") == "default_value"
    
    def test_set_nested_config_value(self, temp_dir, sample_config):
        """Test setting nested configuration values using dot notation."""
        config_file = temp_dir / "test_config.yaml"
        manager = ConfigManager(str(config_file), sample_config)
        
        # Set existing value
        manager.set("api.timeout", 60)
        assert manager.get("api.timeout") == 60
        
        # Set new nested value
        manager.set("new.nested.value", "test")
        assert manager.get("new.nested.value") == "test"
        
        # Set top-level value
        manager.set("debug_mode", True)
        assert manager.get("debug_mode") is True
    
    @patch("builtins.open", new_callable=mock_open)
    @patch("yaml.safe_dump")
    def test_save_config(self, mock_yaml_dump, mock_file, temp_dir, sample_config):
        """Test saving configuration to file."""
        config_file = temp_dir / "test_config.yaml"
        manager = ConfigManager(str(config_file), sample_config)
        
        result = manager.save_config()
        
        assert result is True
        mock_file.assert_called_once_with(str(config_file), 'w', encoding='utf-8')
        mock_yaml_dump.assert_called_once()
    
    @patch("builtins.open", side_effect=OSError("Permission denied"))
    def test_save_config_failure(self, mock_file, temp_dir, sample_config):
        """Test handling of save configuration failure."""
        config_file = temp_dir / "test_config.yaml"
        manager = ConfigManager(str(config_file), sample_config)
        
        result = manager.save_config()
        
        assert result is False
    
    def test_load_config_from_file(self, temp_dir, sample_config):
        """Test loading configuration from existing file."""
        config_file = temp_dir / "test_config.yaml"
        
        # Create config file
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.safe_dump(sample_config, f)
        
        manager = ConfigManager(str(config_file))
        loaded_config = manager.load_config()
        
        assert loaded_config == sample_config
        assert manager.config == sample_config
    
    def test_load_config_file_not_found(self, temp_dir):
        """Test loading configuration when file doesn't exist."""
        config_file = temp_dir / "nonexistent_config.yaml"
        manager = ConfigManager(str(config_file))
        
        loaded_config = manager.load_config()
        
        assert loaded_config == {}
        assert manager.config == {}
    
    @patch("builtins.open", new_callable=mock_open, read_data="invalid: yaml: content:")
    @patch("yaml.safe_load", side_effect=yaml.YAMLError("Invalid YAML"))
    def test_load_config_invalid_yaml(self, mock_yaml_load, mock_file, temp_dir):
        """Test loading configuration with invalid YAML."""
        config_file = temp_dir / "invalid_config.yaml"
        manager = ConfigManager(str(config_file))
        
        loaded_config = manager.load_config()
        
        assert loaded_config == {}
        assert manager.config == {}
    
    def test_override_from_cli(self, temp_dir, sample_config):
        """Test overriding configuration values from CLI arguments."""
        config_file = temp_dir / "test_config.yaml"
        manager = ConfigManager(str(config_file), sample_config)
        
        cli_overrides = {
            "api.timeout": 45,
            "paths.output_dir": "/new/output/path",
            "new_setting": "from_cli"
        }
        
        manager.override_from_cli(cli_overrides)
        
        assert manager.get("api.timeout") == 45
        assert manager.get("paths.output_dir") == "/new/output/path"
        assert manager.get("new_setting") == "from_cli"
        
        # Original values should be preserved if not overridden
        assert manager.get("api.deepseek_api_key") == "test_key_12345"
    
    def test_validate_config_valid(self, temp_dir, sample_config):
        """Test configuration validation with valid config."""
        config_file = temp_dir / "test_config.yaml"
        manager = ConfigManager(str(config_file), sample_config)
        
        # Should not raise any exceptions
        manager.validate_config()
    
    def test_validate_config_missing_required(self, temp_dir):
        """Test configuration validation with missing required fields."""
        config_file = temp_dir / "test_config.yaml"
        incomplete_config = {"app": {"name": "Test App"}}
        manager = ConfigManager(str(config_file), incomplete_config)
        
        # Should not raise exceptions but may show warnings
        manager.validate_config()


class TestConfigManagerUtilities:
    """Test utility functions for configuration management."""
    
    @patch("config.ConfigManager")
    def test_get_config_manager_singleton(self, mock_config_manager):
        """Test that get_config_manager returns a singleton instance."""
        # Clear any existing instance
        if hasattr(get_config_manager, '_instance'):
            delattr(get_config_manager, '_instance')
        
        # First call should create instance
        instance1 = get_config_manager()
        
        # Second call should return same instance
        instance2 = get_config_manager()
        
        assert instance1 is instance2
        mock_config_manager.assert_called_once()


@pytest.mark.unit
class TestConfigIntegration:
    """Integration tests for configuration system."""
    
    def test_full_config_workflow(self, temp_dir):
        """Test complete configuration workflow: create, modify, save, load."""
        config_file = temp_dir / "workflow_config.yaml"
        
        # Initial configuration
        initial_config = {
            "app": {"name": "Test App", "version": "1.0.0"},
            "settings": {"debug": False}
        }
        
        # Step 1: Create and save initial config
        manager = ConfigManager(str(config_file), initial_config)
        save_result = manager.save_config()
        assert save_result is True
        assert config_file.exists()
        
        # Step 2: Modify configuration
        manager.set("settings.debug", True)
        manager.set("new_feature.enabled", True)
        
        # Step 3: Save modifications
        save_result = manager.save_config()
        assert save_result is True
        
        # Step 4: Load with new manager instance
        new_manager = ConfigManager(str(config_file))
        loaded_config = new_manager.load_config()
        
        # Verify all changes were persisted
        assert new_manager.get("app.name") == "Test App"
        assert new_manager.get("settings.debug") is True
        assert new_manager.get("new_feature.enabled") is True 