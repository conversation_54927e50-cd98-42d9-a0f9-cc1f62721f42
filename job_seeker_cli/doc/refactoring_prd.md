# Job Seeker CLI 重构项目需求文档 (PRD)

## 概述 (Overview)

### 项目背景
Job Seeker CLI 是一个求职自动化命令行工具，当前存在代码冗余和架构问题。主要问题包括：
- `menu_system.py` 文件过度臃肿（~1200行），违反单一职责原则
- 依赖外部 `auto-mation` 目录，缺乏自包含性
- 重复的代码模式导致维护困难
- UI逻辑与业务逻辑混合

### 解决的问题
1. **代码冗余**：消除重复的文件选择、错误处理、用户交互模式
2. **架构混乱**：实现关注点分离，提高代码可维护性
3. **依赖外部**：创建自包含架构，独立管理数据和输出
4. **扩展困难**：建立清晰的模块化结构，便于功能扩展

### 目标用户
- 开发者：需要维护和扩展CLI功能
- 最终用户：需要稳定可靠的求职自动化工具

## 核心功能 (Core Features)

### 1. 模块化架构重构
**功能描述**：将臃肿的 `menu_system.py` 拆分为专门的模块
- **重要性**：提高代码可读性和可维护性
- **实现方式**：创建 `workflows/`、`ui/`、`services/` 目录结构

### 2. 自包含数据管理
**功能描述**：集成 `auto-mation` 脚本功能，实现独立的数据路径管理
- **重要性**：消除对外部目录的依赖，提高部署便利性
- **实现方式**：创建 `PathManager` 服务和独立的 `data/`、`output/` 目录

### 3. 通用服务抽取
**功能描述**：抽取文件选择、错误处理、用户交互等通用逻辑
- **重要性**：消除代码重复，提高代码复用性
- **实现方式**：创建 `BaseWorkflow` 类和通用服务模块

### 4. 工作流标准化
**功能描述**：标准化求职流程（爬取→分析→生成求职信）
- **重要性**：统一工作流接口，便于扩展新功能
- **实现方式**：实现继承 `BaseWorkflow` 的专门工作流类

## 用户体验 (User Experience)

### 用户画像
- **开发者**：需要清晰的代码结构，易于修改和扩展功能
- **运维人员**：需要独立部署的应用，不依赖复杂的目录结构
- **最终用户**：需要稳定的CLI界面，功能保持不变

### 核心用户流程
1. **配置阶段**：设置个人信息和API密钥
2. **数据导入**：导入或创建职位数据文件
3. **执行工作流**：选择并执行求职自动化流程
4. **结果查看**：查看生成的分析结果和求职信

### UI/UX 考量
- **向后兼容**：保持现有CLI界面不变
- **错误提示**：统一的错误处理和用户提示
- **数据隔离**：用户数据完全独立管理

## 技术架构 (Technical Architecture)

### 系统组件

```
job_seeker_cli/
├── config/                    # 配置管理
│   ├── __init__.py
│   └── settings.py
├── data/                      # 独立数据目录
│   ├── input/                 # 输入职位数据
│   ├── processed/             # 处理后数据
│   ├── target_jobs/          # 匹配结果
│   └── personal/             # 个人信息
├── output/                    # 独立输出目录
│   ├── cover_letters/        # 求职信
│   └── reports/              # 分析报告
├── workflows/                 # 工作流模块
│   ├── __init__.py
│   ├── base_workflow.py      # 基础工作流类
│   ├── job_scraping.py       # 爬取工作流
│   ├── job_analysis.py       # 分析工作流
│   └── cover_letter.py       # 求职信工作流
├── ui/                       # UI展示模块
│   ├── __init__.py
│   ├── menu_renderer.py      # 菜单渲染
│   ├── user_interaction.py   # 用户交互
│   └── display_utils.py      # 显示工具
├── services/                  # 服务模块
│   ├── __init__.py
│   ├── path_manager.py       # 路径管理
│   ├── file_service.py       # 文件服务
│   ├── api_client.py         # API客户端
│   └── validation_service.py # 验证服务
├── scripts/                   # 集成的核心逻辑
│   ├── __init__.py
│   ├── job_fetcher.py        # 爬取逻辑
│   ├── job_analyzer.py       # 分析逻辑
│   └── letter_generator.py   # 求职信生成
└── menu_system.py            # 简化的主控制器
```

### 数据模型
- **PathManager**：统一管理所有文件路径
- **JobDataFile**：职位数据文件结构
- **WorkflowResult**：工作流执行结果
- **UserConfig**：用户配置数据

### API和集成
- **DeepSeek API**：智能分析和求职信生成
- **文件系统**：本地数据存储和管理
- **Rich库**：命令行界面美化

### 基础设施要求
- **Python 3.8+**：运行环境
- **依赖包**：Rich、Click、Requests等
- **文件系统**：读写权限

## 开发路线图 (Development Roadmap)

### 第一阶段：基础架构搭建
**MVP要求**：
- 创建新目录结构
- 实现 `PathManager` 服务
- 创建 `BaseWorkflow` 基类
- 保持现有功能可用

**具体任务**：
1. 创建 `config/`、`data/`、`output/`、`workflows/`、`ui/`、`services/`、`scripts/` 目录
2. 实现 `services/path_manager.py`
3. 实现 `workflows/base_workflow.py`
4. 实现 `services/file_service.py`

### 第二阶段：脚本集成与路径重构
**目标**：实现自包含架构
- 复制并改造 `auto-mation` 脚本
- 修改所有硬编码路径
- 测试数据流完整性

**具体任务**：
1. 实现 `scripts/job_fetcher.py`（基于 `auto-mation/fetch_job_details.py`）
2. 实现 `scripts/job_analyzer.py`（基于 `auto-mation/analyze_job_match.py`）
3. 实现 `scripts/letter_generator.py`（基于 `auto-mation/generate_coverletter.py`）
4. 实现 `services/api_client.py`

### 第三阶段：工作流模块化
**目标**：拆分 `menu_system.py`
- 实现专门的工作流类
- 提取通用交互逻辑
- 实现UI与业务逻辑分离

**具体任务**：
1. 实现 `workflows/job_scraping.py`
2. 实现 `workflows/job_analysis.py`
3. 实现 `workflows/cover_letter.py`
4. 实现 `ui/menu_renderer.py`
5. 实现 `ui/user_interaction.py`

### 第四阶段：优化与完善
**目标**：消除剩余冗余，完善功能
- 重构简化 `menu_system.py`
- 分离 `data_manager.py` 的UI逻辑
- 添加配置管理功能

**具体任务**：
1. 重构 `menu_system.py`
2. 实现 `ui/display_utils.py`
3. 重构 `data_manager.py`
4. 实现 `config/settings.py`
5. 添加数据迁移工具

## 逻辑依赖链 (Logical Dependency Chain)

### 开发顺序
1. **基础服务** → **工作流基类** → **UI组件**
2. **路径管理** → **脚本集成** → **工作流实现**
3. **核心功能** → **界面优化** → **配置管理**

### 关键里程碑
1. **可运行的基础架构**：PathManager + BaseWorkflow
2. **自包含功能**：集成所有auto-mation脚本逻辑
3. **模块化完成**：menu_system.py重构完成
4. **生产就绪**：所有测试通过，文档完善

### 原子化范围
- 每个模块独立可测试
- 每个工作流可独立运行
- 每个服务可独立使用
- 保持向后兼容性

## 风险和缓解措施 (Risks and Mitigations)

### 技术挑战
**风险**：路径重构可能破坏现有功能
**缓解措施**：
- 实现PathManager统一管理路径
- 保留原有数据格式兼容性
- 分阶段迁移，每步验证功能完整性

**风险**：API集成可能出现兼容性问题
**缓解措施**：
- 抽象API客户端接口
- 保持与原有API调用方式一致
- 添加错误处理和重试机制

### MVP确定
**风险**：功能范围过大，影响交付时间
**缓解措施**：
- 优先保证核心功能（求职流程）可用
- 分阶段交付，每阶段都有可用产品
- 关注用户反馈，及时调整优先级

### 资源约束
**风险**：重构工作量大，可能影响其他开发
**缓解措施**：
- 采用渐进式重构策略
- 保持现有功能稳定运行
- 新老代码并存，逐步替换

## 附录 (Appendix)

### 研究发现
- `menu_system.py` 包含5处重复的文件选择逻辑
- `auto-mation` 脚本使用3种不同的路径模式
- 错误处理代码重复出现8次
- UI显示逻辑分散在3个文件中

### 技术规范
- **编码标准**：遵循PEP 8 Python编码规范
- **文档要求**：每个模块必须包含docstring和类型注解
- **测试要求**：核心逻辑需要单元测试覆盖
- **性能要求**：重构后性能不得低于原系统

### 预期收益
- **代码量减少**：menu_system.py从1200行减少到300行（减少75%）
- **可维护性**：模块化架构提高代码可读性
- **扩展性**：新功能开发效率提升50%
- **部署便利性**：自包含架构支持独立部署

---

*本PRD文档版本：v1.0*  
*创建日期：2025-06-21*  
*负责人：项目重构团队*
