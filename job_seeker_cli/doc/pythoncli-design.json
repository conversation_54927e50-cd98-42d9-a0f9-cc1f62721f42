{"designSystem": {"name": "Python CLI Design System", "version": "1.0.0", "description": "基于 awesome_cli.py 的完整设计系统，用于复制其精确的视觉外观和交互体验", "baseFile": "/Users/<USER>/Desktop/cli-tool/awesome_cli.py"}, "dependencies": {"core": {"rich": {"version": ">=13.0.0", "purpose": "核心UI渲染库，提供颜色、布局、面板、表格等功能", "components": ["Console - 主控制台对象", "Panel - 带边框的内容容器", "Text - 富文本对象，支持样式", "Table - 表格组件", "Layout - 布局容器，支持分割", "Align - 对齐组件", "progress.track - 进度跟踪"]}, "pyfiglet": {"version": ">=0.8.0", "purpose": "ASCII艺术字生成", "fonts": ["ansi_shadow - 主标题字体", "doom - 复古风格", "cyberlarge - 未来风格", "rectangles - 像素风格", "nancyj - 霓虹风格"]}, "questionary": {"version": ">=1.10.0", "purpose": "交互式命令行界面", "components": ["select - 选择菜单", "confirm - 确认对话框", "press_any_key_to_continue - 暂停等待"]}, "click": {"version": ">=8.0.0", "purpose": "命令行框架", "features": ["@click.group() - 命令组", "@click.version_option() - 版本选项", "@cli.command() - 子命令"]}}, "system": {"psutil": {"version": ">=5.8.0", "purpose": "系统信息获取", "usage": "CPU、内存、启动时间等系统数据"}, "platform": {"version": "内置模块", "purpose": "平台信息获取", "usage": "操作系统、架构信息"}, "os": {"version": "内置模块", "purpose": "操作系统接口", "usage": "用户名、路径等"}, "time": {"version": "内置模块", "purpose": "时间处理", "usage": "时间格式化、延迟"}}}, "typography": {"asciiFonts": {"primary": {"name": "ansi_shadow", "usage": "主标题 AWESOME CLI", "fallback": "default", "style": "bold red"}, "gallery": [{"name": "doom", "text": "RETRO", "color": "red", "style": "bold"}, {"name": "cyberlarge", "text": "FUTURE", "color": "blue", "style": "bold"}, {"name": "rectangles", "text": "PIXEL", "color": "magenta", "style": "bold"}, {"name": "nancy<PERSON>", "text": "NEON", "color": "green", "style": "bold"}]}, "textStyles": {"title": "bold cyan", "subtitle": "bold white", "body": "white", "caption": "dim", "emphasis": "bold", "code": "green"}}, "components": {"panel": {"welcome": {"content": "[cyan]✨ Welcome to Awesome CLI Tool ✨[/cyan]", "padding": "(1, 2)", "borderStyle": "cyan", "title": "[cyan]欢迎[/cyan]"}, "statusBar": {"top": {"content": "[bold red]🎯 顶部状态栏 - 当前时间: {time}[/bold red]", "borderStyle": "red", "size": 3}, "bottom": {"content": "[bold blue]📊 底部信息栏 - 用户: {user} | 版本: {version}[/bold blue]", "borderStyle": "blue", "size": 3}}, "sidebar": {"content": "[bold green]📋 左侧菜单[/bold green]", "borderStyle": "green", "title": "[green]菜单[/green]", "size": 30}, "mainContent": {"content": "[bold yellow]📄 主要内容区域[/bold yellow]", "borderStyle": "yellow", "title": "[yellow]内容[/yellow]"}}, "table": {"systemInfo": {"title": "📊 系统信息", "borderStyle": "yellow", "headerStyle": "bold magenta", "columns": [{"name": "项目", "style": "cyan"}, {"name": "值", "style": "white"}]}, "layoutDetails": {"title": "📐 布局系统详细信息", "borderStyle": "blue", "headerStyle": "bold magenta", "columns": [{"name": "组件", "style": "cyan", "width": 20}, {"name": "描述", "style": "white", "width": 40}, {"name": "特性", "style": "green", "width": 25}]}}, "menu": {"main": {"prompt": "🚀 请选择要执行的操作:", "choices": ["🎨 显示 ASCII 艺术画廊", "🔧 创建新项目", "📊 系统信息", "📐 布局设计演示", "🎯 运行测试", "📋 显示帮助", "🚪 退出"]}, "demo": {"prompt": "选择一个操作:", "choices": ["🔄 刷新布局", "🎨 更换主题", "📊 显示详细信息", "🔙 返回主菜单"]}}, "status": {"icons": {"success": "✅", "error": "❌", "info": "ℹ️", "warning": "⚠️", "user": "👤"}, "format": "[{color}]{icon} {message}[/{color}]"}, "spinner": {"type": "dots12", "format": "[cyan]{text}[/cyan]", "completion": "[green]✅ 操作完成！[/green]"}}, "interactions": {"navigation": {"type": "questionary.select", "controls": "方向键导航，回车确认", "style": "默认questionary样式"}, "confirmation": {"type": "questionary.confirm", "usage": "退出确认等场景"}, "pause": {"type": "questionary.press_any_key_to_continue", "usage": "查看完内容后的暂停"}}, "animations": {"loading": {"spinner": "dots12", "color": "cyan", "duration": "可变 (1-3秒)"}, "progress": {"type": "rich.progress.track", "description": "运行测试中...", "style": "默认进度条样式"}}, "content": {"welcome": {"banner": {"text": "AWESOME\nCLI", "font": "ansi_shadow", "style": "bold red"}, "message": "✨ Welcome to Awesome CLI Tool ✨"}, "help": {"sections": [{"title": "🔧 命令说明:", "style": "cyan bold"}, {"title": "🎨 功能特色:", "style": "cyan bold"}, {"title": "💡 提示:", "style": "yellow"}]}, "systemInfo": {"items": ["🖥️ 操作系统", "💻 CPU架构", "🧠 CPU核心", "💾 总内存", "🆓 可用内存", "👤 用户", "🏠 主目录", "⏰ 运行时间"]}}, "bestPractices": {"errorHandling": {"fontFallback": "ASCII字体不可用时使用默认字体", "exceptionCatch": "KeyboardInterrupt和通用异常处理"}, "userExperience": {"clearScreen": "每次显示新内容前清屏", "feedback": "操作完成后显示确认信息", "navigation": "提供返回和退出选项"}, "performance": {"lazyLoading": "按需创建布局和内容", "resourceCleanup": "适当的资源管理"}}, "usage": {"replication": {"steps": ["1. 安装所有依赖包", "2. 创建AwesomeCLI类", "3. 实现各个display方法", "4. 配置颜色和主题", "5. 设置布局结构", "6. 添加交互逻辑", "7. 实现命令行接口"], "keyPoints": ["严格遵循颜色方案", "保持布局尺寸一致", "使用相同的字体和样式", "复制交互流程", "保持视觉层次结构"]}, "customization": {"colors": "修改colorPalette部分", "fonts": "更新typography.asciiFonts", "layout": "调整layout.dimensions", "content": "更新content部分的文本"}}}