from typing import Dict, Any, List

class ValidationService:
    """
    Provides data validation services for different data types in the application.
    """

    def validate_job_data(self, job: Dict[str, Any]) -> List[str]:
        """
        Validates a single job data dictionary.

        Args:
            job (Dict[str, Any]): The job data to validate.

        Returns:
            List[str]: A list of validation error messages. Empty if valid.
        """
        errors = []
        required_fields = ["jobAdvertiser", "jobTitle", "recommendedJobLink"]
        
        for field in required_fields:
            if field not in job or not job[field]:
                errors.append(f"Missing or empty required field: '{field}'")
        
        return errors

    def validate_config(self, settings: Any) -> List[str]:
        """
        Validates the application settings object.

        Args:
            settings (Any): The settings object to validate.

        Returns:
            List[str]: A list of configuration error messages.
        """
        errors = []
        if not getattr(settings, 'deepseek_api_key', None):
            errors.append("DEEPSEEK_API_KEY is not configured.")
        
        return errors

if __name__ == '__main__':
    validator = ValidationService()

    # Test job data validation
    valid_job = {"jobAdvertiser": "Test Co", "jobTitle": "Developer", "recommendedJobLink": "http://a.com"}
    invalid_job = {"jobTitle": "Developer"}
    
    print("Validating a valid job:", validator.validate_job_data(valid_job))
    print("Validating an invalid job:", validator.validate_job_data(invalid_job))

    # Test config validation
    class MockSettings:
        def __init__(self, api_key=None):
            self.deepseek_api_key = api_key
            
    valid_config = MockSettings(api_key="some_key")
    invalid_config = MockSettings()
    
    print("Validating a valid config:", validator.validate_config(valid_config))
    print("Validating an invalid config:", validator.validate_config(invalid_config))
