import os
from pathlib import Path
from typing import Dict, List

class PathManager:
    """
    Manages all file paths for the application, ensuring a consistent directory structure.
    """
    def __init__(self, base_dir: str = None):
        self.base_dir = Path(base_dir) if base_dir else Path(__file__).parent.parent
        self.paths: Dict[str, Path] = {
            "base": self.base_dir,
            "config": self.base_dir / "config",
            "data": self.base_dir / "data",
            "input": self.base_dir / "data" / "input",
            "processed": self.base_dir / "data" / "processed",
            "target_jobs": self.base_dir / "data" / "target_jobs",
            "personal": self.base_dir / "data" / "personal",
            "output": self.base_dir / "output",
            "cover_letters": self.base_dir / "output" / "cover_letters",
            "reports": self.base_dir / "output" / "reports",
            "workflows": self.base_dir / "workflows",
            "ui": self.base_dir / "ui",
            "services": self.base_dir / "services",
            "scripts": self.base_dir / "scripts",
        }
        self._create_directories()

    def _create_directories(self):
        """Create all necessary directories if they don't exist."""
        for path in self.paths.values():
            os.makedirs(path, exist_ok=True)

    def get_path(self, name: str) -> Path:
        """
        Get a path by its registered name.

        Args:
            name (str): The name of the path to retrieve.

        Returns:
            Path: The corresponding path object.
        
        Raises:
            KeyError: If the name is not registered.
        """
        if name not in self.paths:
            raise KeyError(f"Path '{name}' is not a registered path.")
        return self.paths[name]

    def get_all_paths(self) -> Dict[str, Path]:
        """Returns all registered paths."""
        return self.paths

# Example usage:
if __name__ == "__main__":
    path_manager = PathManager()
    print("Base directory:", path_manager.get_path("base"))
    print("Input data path:", path_manager.get_path("input"))
    print("Cover letters output path:", path_manager.get_path("cover_letters"))
