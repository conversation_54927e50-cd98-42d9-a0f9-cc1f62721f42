import requests
import json
from typing import Dict, Any, Optional

class ApiClient:
    """
    A unified client for making requests to external APIs.
    """
    def __init__(self, api_key: str, api_url: str):
        if not api_key:
            raise ValueError("API key cannot be empty.")
        self.api_key = api_key
        self.api_url = api_url
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }

    def post(self, data: Dict[str, Any], timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        Sends a POST request to the configured API endpoint.

        Args:
            data (Dict[str, Any]): The JSON payload to send.
            timeout (int): The request timeout in seconds.

        Returns:
            Optional[Dict[str, Any]]: The JSON response from the API, or None on error.
        """
        try:
            response = requests.post(self.api_url, headers=self.headers, json=data, timeout=timeout)
            response.raise_for_status()  # Raise an exception for bad status codes
            return response.json()
        except requests.RequestException as e:
            print(f"API request failed: {e}")
            return None
        except json.JSONDecodeError:
            print("Failed to parse API response as JSON.")
            return None

if __name__ == '__main__':
    # This is an example of how to use the ApiClient.
    # You need to have a valid API key and a running endpoint to test this.
    # api_key = "your_deepseek_api_key"
    # api_url = "https://api.deepseek.com/chat/completions"
    
    # client = ApiClient(api_key, api_url)
    
    # sample_payload = {
    #     'model': 'deepseek-chat',
    #     'messages': [
    #         {'role': 'user', 'content': 'Hello!'}
    #     ]
    # }
    
    # response = client.post(sample_payload)
    # if response:
    #     print("API Response:", response)
    pass
