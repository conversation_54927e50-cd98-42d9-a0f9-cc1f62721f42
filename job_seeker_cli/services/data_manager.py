# job_seeker_cli/services/data_manager.py
# 数据管理服务，用于处理职位数据文件的扫描、验证和统计
# 这个服务专门为CLI命令提供数据管理功能

import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from rich.console import Console
from rich.table import Table

from job_seeker_cli.services.file_service import FileService

console = Console()

# 职位数据的基本模式验证
JOB_SCHEMA = {
    "required_fields": ["jobAdvertiser", "jobTitle", "recommendedJobLink"],
    "optional_fields": ["jobDescription", "jobLocation", "salary", "datePosted"]
}

@dataclass
class JobDataFile:
    """表示一个职位数据文件的信息"""
    filename: str
    filepath: Path
    is_valid: bool = False
    job_count: int = 0
    validation_errors: List[str] = None
    
    def __post_init__(self):
        if self.validation_errors is None:
            self.validation_errors = []

class DataManager:
    """
    增强的数据管理器，用于扫描、验证和管理职位数据文件
    集成到新的模块化架构中
    """
    
    def __init__(self, job_data_dir: str):
        self.job_data_dir = Path(job_data_dir)
        self.job_files: List[JobDataFile] = []
    
    def scan_directory(self) -> int:
        """
        扫描指定目录中的JSON文件
        
        Returns:
            int: 找到的文件数量
        """
        self.job_files.clear()
        
        if not self.job_data_dir.exists():
            self.job_data_dir.mkdir(parents=True, exist_ok=True)
            return 0
        
        json_files = list(self.job_data_dir.glob("*.json"))
        
        for json_file in json_files:
            job_file = JobDataFile(
                filename=json_file.name,
                filepath=json_file
            )
            
            # 验证文件
            self._validate_job_file(job_file)
            self.job_files.append(job_file)
        
        return len(self.job_files)
    
    def _validate_job_file(self, job_file: JobDataFile) -> None:
        """验证单个职位数据文件"""
        try:
            with open(job_file.filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                job_file.validation_errors.append("文件应包含职位数组")
                return
            
            job_file.job_count = len(data)
            valid_jobs = 0
            
            for i, job in enumerate(data):
                if not isinstance(job, dict):
                    job_file.validation_errors.append(f"第{i+1}个条目不是有效的对象")
                    continue
                
                # 检查必需字段
                missing_fields = []
                for field in JOB_SCHEMA["required_fields"]:
                    if field not in job or not job[field]:
                        missing_fields.append(field)
                
                if missing_fields:
                    job_file.validation_errors.append(
                        f"第{i+1}个职位缺少字段: {', '.join(missing_fields)}"
                    )
                else:
                    valid_jobs += 1
            
            # 如果所有职位都有效，标记文件为有效
            job_file.is_valid = (valid_jobs == job_file.job_count and job_file.job_count > 0)
            
        except json.JSONDecodeError as e:
            job_file.validation_errors.append(f"JSON格式错误: {e}")
        except Exception as e:
            job_file.validation_errors.append(f"读取文件错误: {e}")
    
    def get_summary_stats(self) -> Dict[str, int]:
        """获取汇总统计信息"""
        total_files = len(self.job_files)
        valid_files = sum(1 for f in self.job_files if f.is_valid)
        total_jobs = sum(f.job_count for f in self.job_files if f.is_valid)
        
        return {
            "total_files": total_files,
            "valid_files": valid_files,
            "invalid_files": total_files - valid_files,
            "total_jobs": total_jobs
        }
    
    def display_files_table(self, show_invalid: bool = True, limit: Optional[int] = None) -> None:
        """以表格形式显示文件列表"""
        if not self.job_files:
            console.print("📭 没有找到职位数据文件")
            return
        
        table = Table(title="📊 职位数据文件")
        table.add_column("文件名", style="cyan")
        table.add_column("状态", style="bold")
        table.add_column("职位数", justify="right")
        table.add_column("大小", justify="right")
        
        files_to_show = self.job_files
        if not show_invalid:
            files_to_show = [f for f in self.job_files if f.is_valid]
        
        if limit:
            files_to_show = files_to_show[:limit]
        
        for job_file in files_to_show:
            status = "✅ 有效" if job_file.is_valid else "❌ 无效"
            size = self._format_file_size(job_file.filepath.stat().st_size)
            
            table.add_row(
                job_file.filename,
                status,
                str(job_file.job_count),
                size
            )
        
        console.print(table)
        
        # 显示汇总
        stats = self.get_summary_stats()
        console.print(f"\n📈 汇总: {stats['valid_files']}/{stats['total_files']} 有效文件, 共 {stats['total_jobs']} 个职位")
    
    def display_file_details(self, filename: str) -> bool:
        """显示特定文件的详细信息"""
        job_file = next((f for f in self.job_files if f.filename == filename), None)
        
        if not job_file:
            console.print(f"❌ 文件未找到: {filename}")
            return False
        
        console.print(f"\n📄 [bold]{job_file.filename}[/bold]")
        console.print(f"路径: {job_file.filepath}")
        console.print(f"状态: {'✅ 有效' if job_file.is_valid else '❌ 无效'}")
        console.print(f"职位数量: {job_file.job_count}")
        console.print(f"文件大小: {self._format_file_size(job_file.filepath.stat().st_size)}")
        
        if job_file.validation_errors:
            console.print("\n⚠️ [bold red]验证错误:[/bold red]")
            for error in job_file.validation_errors:
                console.print(f"  • {error}")
        
        return True
    
    def create_sample_data_directory(self) -> bool:
        """创建示例数据目录和文件"""
        try:
            self.job_data_dir.mkdir(parents=True, exist_ok=True)
            
            sample_data = [
                {
                    "jobAdvertiser": "Tech Solutions Ltd",
                    "jobTitle": "Senior Python Developer",
                    "recommendedJobLink": "https://example.com/job/1",
                    "jobLocation": "Sydney, NSW",
                    "salary": "120000-150000",
                    "datePosted": "2024-06-15"
                },
                {
                    "jobAdvertiser": "Data Corp",
                    "jobTitle": "Machine Learning Engineer",
                    "recommendedJobLink": "https://example.com/job/2",
                    "jobLocation": "Melbourne, VIC",
                    "salary": "130000-160000",
                    "datePosted": "2024-06-18"
                }
            ]
            
            sample_file = self.job_data_dir / "sample_jobs.json"
            with open(sample_file, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, indent=2, ensure_ascii=False)
            
            console.print(f"✅ 示例数据已创建: {sample_file}")
            return True
            
        except Exception as e:
            console.print(f"❌ 创建示例数据失败: {e}")
            return False
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
