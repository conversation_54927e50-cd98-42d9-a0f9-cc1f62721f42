# /Users/<USER>/Desktop/aus_job/job_seeker_cli/ui/user_interaction.py
# 处理用户输入和交互的组件
# 提供基于Textual的现代化交互界面，支持方向键导航和emoji美化

from typing import Dict, Any, Optional, List
from rich.prompt import Prompt, Confirm, IntPrompt
from rich.console import Console

# 导入 questionary 用于交互式菜单
try:
    import questionary
    QUESTIONARY_AVAILABLE = True
except ImportError:
    QUESTIONARY_AVAILABLE = False
    print("警告: questionary未安装，将使用传统菜单")

# 导入新的Textual菜单作为备选
try:
    from .textual_menu import show_menu_sync
    TEXTUAL_AVAILABLE = True
except ImportError:
    TEXTUAL_AVAILABLE = False

class UserInteraction:
    """
    Handles user input and interaction using questionary and rich libraries.
    Based on awesome_cli.py design system.
    """
    def __init__(self):
        self.console = Console()
        self.use_questionary = QUESTIONARY_AVAILABLE
        self.use_textual = TEXTUAL_AVAILABLE

    def get_choice(self, options: Dict[str, Any]) -> Optional[Any]:
        """
        Gets a choice from the user based on the provided options.
        Uses questionary for interactive selection, following awesome_cli.py design.

        Args:
            options (Dict[str, Any]): A dictionary of options.

        Returns:
            Optional[Any]: The value of the selected option, or None for exit/back.
        """
        if self.use_questionary and QUESTIONARY_AVAILABLE:
            try:
                # 准备选项列表
                choices = list(options.keys()) + ["🚪 退出"]
                
                # 使用questionary进行选择
                selected = questionary.select(
                    "🚀 请选择要执行的操作:",
                    choices=choices,
                    style=questionary.Style([
                        ('question', 'bold cyan'),
                        ('answer', 'bold green'),
                        ('pointer', 'bold yellow'),
                        ('highlighted', 'bold blue'),
                        ('selected', 'bold green'),
                    ])
                ).ask()
                
                if selected is None or selected == "🚪 退出":
                    return None
                    
                return options[selected]
                
            except (KeyboardInterrupt, EOFError):
                return None
            except Exception as e:
                self.console.print(f"[yellow]questionary菜单出错，切换到传统菜单: {e}[/yellow]")
                self.use_questionary = False
        
        # Textual菜单作为备选
        if self.use_textual and TEXTUAL_AVAILABLE:
            try:
                return show_menu_sync("主菜单", options)
            except Exception as e:
                self.console.print(f"[yellow]Textual菜单出错，切换到传统菜单: {e}[/yellow]")
                self.use_textual = False
        
        # 传统数字菜单作为最后备选
        num_options = len(options)
        choice = IntPrompt.ask(
            "[bold cyan]Choose an option[/bold cyan]",
            choices=[str(i) for i in range(num_options + 1)],
            show_choices=False
        )
        
        if choice == 0:
            return None
        
        return list(options.values())[choice - 1]

    def get_confirmation(self, prompt: str) -> bool:
        """
        Gets a yes/no confirmation from the user.
        Uses questionary for better UX.

        Args:
            prompt (str): The confirmation prompt to display.

        Returns:
            bool: True for yes, False for no.
        """
        if self.use_questionary and QUESTIONARY_AVAILABLE:
            try:
                return questionary.confirm(prompt).ask()
            except (KeyboardInterrupt, EOFError):
                return False
            except Exception:
                pass
        
        # Fallback to rich Confirm
        return Confirm.ask(prompt)

    def select_from_list(self, prompt: str, items: List[str]) -> Optional[str]:
        """
        Prompts the user to select an item from a list.
        Uses questionary for interactive selection.

        Args:
            prompt (str): The prompt to display.
            items (List[str]): The list of items to choose from.

        Returns:
            Optional[str]: The selected item, or None if canceled.
        """
        if not items:
            self.console.print("[yellow]❌ 没有可选择的项目。[/yellow]")
            return None

        # 使用questionary进行选择
        if self.use_questionary and QUESTIONARY_AVAILABLE:
            try:
                choices = items + ["❌ 取消"]
                selected = questionary.select(
                    prompt,
                    choices=choices,
                    style=questionary.Style([
                        ('question', 'bold cyan'),
                        ('answer', 'bold green'),
                        ('pointer', 'bold yellow'),
                        ('highlighted', 'bold blue'),
                        ('selected', 'bold green'),
                    ])
                ).ask()
                
                if selected is None or selected == "❌ 取消":
                    return None
                    
                return selected
                
            except (KeyboardInterrupt, EOFError):
                return None
            except Exception as e:
                self.console.print(f"[yellow]questionary出错，切换到传统菜单: {e}[/yellow]")
                self.use_questionary = False

        # 如果可以使用Textual菜单，创建选项字典
        if self.use_textual and TEXTUAL_AVAILABLE:
            try:
                options = {item: item for item in items}
                result = show_menu_sync(prompt, options)
                return result
            except Exception as e:
                self.console.print(f"[yellow]Textual菜单出错，切换到传统菜单: {e}[/yellow]")
                self.use_textual = False

        # 传统列表选择作为后备
        self.console.print(f"\n[bold blue]{prompt}[/bold blue]")
        for i, item in enumerate(items):
            self.console.print(f"  [cyan]{i + 1}[/cyan]. {item}")
        self.console.print("  [cyan]0[/cyan]. ❌ 取消")

        choice = IntPrompt.ask(
            "请输入您的选择",
            choices=[str(i) for i in range(len(items) + 1)],
            show_choices=False
        )

        if choice == 0:
            return None
        
        return items[choice - 1]

if __name__ == '__main__':
    interaction = UserInteraction()
    
    sample_options = {
        "Scrape Job Details": "scrape",
        "Analyze Job Matches": "analyze",
        "Generate Cover Letters": "generate"
    }
    
    # 测试Textual菜单
    print("测试主菜单:")
    selected_option = interaction.get_choice(sample_options)
    if selected_option:
        print(f"You selected: {selected_option}")
    else:
        print("You chose to exit.")

    if interaction.get_confirmation("Are you sure?"):
        print("Confirmed.")
    else:
        print("Cancelled.")
        
    items = ["file1.json", "file2.json", "file3.json"]
    selected_item = interaction.select_from_list("Select a file", items)
    if selected_item:
        print(f"You selected: {selected_item}")
