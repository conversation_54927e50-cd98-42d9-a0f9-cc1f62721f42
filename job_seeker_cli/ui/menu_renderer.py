from typing import Dict, Any, List
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.align import Align
from rich.layout import Layout
from rich.spinner import Spinner
import os
import time
import platform
import getpass
try:
    import pyfiglet
    PYFIGLET_AVAILABLE = True
except ImportError:
    PYFIGLET_AVAILABLE = False
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

class MenuRenderer:
    """
    Renders menus and other UI components for the console using the 'rich' library.
    Based on awesome_cli.py design system.
    """
    def __init__(self):
        self.console = Console()
        self.version = "1.0.0"

    def _generate_ascii_title(self) -> str:
        """Generate ASCII title using pyfiglet."""
        if PYFIGLET_AVAILABLE:
            try:
                # Use ansi_shadow font as specified in design
                fig = pyfiglet.Figlet(font='ansi_shadow')
                ascii_art = fig.renderText('JOB SEEKER')
                return ascii_art
            except Exception:
                pass
        
        # Fallback to manual ASCII art
        return """
     ██╗ ██████╗ ██████╗     ███████╗███████╗███████╗██╗  ██╗███████╗██████╗ 
     ██║██╔═══██╗██╔══██╗    ██╔════╝██╔════╝██╔════╝██║ ██╔╝██╔════╝██╔══██╗
     ██║██║   ██║██████╔╝    ███████╗█████╗  █████╗  █████╔╝ █████╗  ██████╔╝
██   ██║██║   ██║██╔══██╗    ╚════██║██╔══╝  ██╔══╝  ██╔═██╗ ██╔══╝  ██╔══██╗
╚█████╔╝╚██████╔╝██████╔╝    ███████║███████╗███████╗██║  ██╗███████╗██║  ██║
 ╚════╝  ╚═════╝ ╚═════╝     ╚══════╝╚══════╝╚══════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
        """

    def _get_system_info(self) -> Dict[str, str]:
        """Get system information for status display."""
        info = {
            "🖥️ 操作系统": platform.system(),
            "💻 CPU架构": platform.machine(),
            "👤 用户": getpass.getuser(),
            "⏰ 当前时间": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if PSUTIL_AVAILABLE:
            try:
                info["🧠 CPU核心"] = str(psutil.cpu_count())
                memory = psutil.virtual_memory()
                info["💾 总内存"] = f"{memory.total // (1024**3)} GB"
                info["🆓 可用内存"] = f"{memory.available // (1024**3)} GB"
            except Exception:
                pass
                
        return info

    def _create_status_bar(self, position: str = "top") -> Panel:
        """Create status bar panel."""
        if position == "top":
            current_time = time.strftime("%H:%M:%S")
            content = f"[bold red]🎯 JOB SEEKER CLI - 当前时间: {current_time}[/bold red]"
            return Panel(
                Align.center(content),
                border_style="red",
                height=3
            )
        else:  # bottom
            user = getpass.getuser()
            content = f"[bold blue]📊 用户: {user} | 版本: {self.version} | 按 Ctrl+C 退出[/bold blue]"
            return Panel(
                Align.center(content),
                border_style="blue",
                height=3
            )

    def display_welcome(self):
        """Displays a welcome panel with simplified layout."""

        # Create top status bar
        top_status = self._create_status_bar("top")
        self.console.print(top_status)
        
        # Generate ASCII title
        ascii_title = self._generate_ascii_title()
        
        # Welcome content with ASCII art
        welcome_content = f"[bold red]{ascii_title}[/bold red]\n\n"
        welcome_content += "[cyan]✨ 自动化求职申请工作流程[/cyan]\n"
        welcome_content += "[cyan]🚀 让求职变得更高效[/cyan]"
        
        welcome_panel = Panel(
            Align.center(welcome_content),
            border_style="cyan",
            title="[cyan]欢迎使用[/cyan]",
            padding=(1, 2)
        )
        
        self.console.print(welcome_panel)
        
        # Create bottom status bar
        bottom_status = self._create_status_bar("bottom")
        self.console.print(bottom_status)
        self.console.print("\n")

    def display_menu(self, title: str, options: Dict[str, Any]):
        """
        Displays a menu with a title and options using Rich styling.
        Following awesome_cli.py design patterns.

        Args:
            title (str): The title of the menu.
            options (Dict[str, Any]): A dictionary of options.
        """
        # Create a styled title
        styled_title = f"[bold cyan]🚀 {title}[/bold cyan]"
        
        # Create menu panel
        menu_content = ""
        for i, key in enumerate(options.keys()):
            menu_content += f"[cyan]{i + 1}[/cyan]. {key}\n"
        menu_content += "[cyan]0[/cyan]. " + ("🚪 退出" if title == "Main Menu" else "🔙 返回")
        
        menu_panel = Panel(
            menu_content,
            border_style="yellow",
            title=styled_title,
            padding=(1, 2)
        )
        
        self.console.print(menu_panel)

    def display_message(self, message: str, is_error: bool = False):
        """
        Displays a formatted message with icons.

        Args:
            message (str): The message to display.
            is_error (bool): If true, formats the message as an error.
        """
        if is_error:
            icon = "❌"
            style = "bold red"
        else:
            icon = "✅"
            style = "bold green"
            
        formatted_message = f"[{style}]{icon} {message}[/{style}]"
        self.console.print(formatted_message)

    def display_table(self, title: str, headers: List[str], rows: List[List[Any]]):
        """
        Displays data in a table following design system.

        Args:
            title (str): The title of the table.
            headers (List[str]): A list of table headers.
            rows (List[List[Any]]): A list of rows, where each row is a list of values.
        """
        table = Table(
            title=f"📊 {title}",
            border_style="yellow",
            header_style="bold magenta"
        )
        
        for header in headers:
            table.add_column(header, justify="left", style="cyan")
        
        for row in rows:
            table.add_row(*[str(item) for item in row])
            
        self.console.print(table)

    def display_system_info(self):
        """Display system information table."""
        info = self._get_system_info()
        headers = ["项目", "值"]
        rows = [[key, value] for key, value in info.items()]
        self.display_table("系统信息", headers, rows)

    def display_loading(self, message: str = "处理中..."):
        """Display loading spinner."""
        with self.console.status(f"[cyan]{message}[/cyan]", spinner="dots12"):
            time.sleep(1)  # Simulate work
        self.console.print("[green]✅ 操作完成！[/green]")

if __name__ == '__main__':
    renderer = MenuRenderer()
    renderer.display_welcome()
    
    sample_options = {
        "🕷️ Scrape Job Details": "scrape",
        "🔍 Analyze Job Matches": "analyze",
        "📝 Generate Cover Letters": "generate"
    }
    renderer.display_menu("Main Menu", sample_options)
    
    renderer.display_message("这是一条成功信息。")
    renderer.display_message("这是一条错误信息。", is_error=True)

    headers = ["ID", "名称", "状态"]
    rows = [[1, "抓取任务", "✅"], [2, "分析任务", "⏳"]]
    renderer.display_table("工作流状态", headers, rows)
    
    renderer.display_system_info()