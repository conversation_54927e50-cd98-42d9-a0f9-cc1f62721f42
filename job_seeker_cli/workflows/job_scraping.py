from job_seeker_cli.workflows.base_workflow import BaseWorkflow
from job_seeker_cli.services.file_service import FileService
from job_seeker_cli.scripts.job_fetcher import JobFetcher

from job_seeker_cli.ui.user_interaction import UserInteraction

class JobScrapingWorkflow(BaseWorkflow):
    def __init__(self, file_service: FileService, interaction: UserInteraction):
        super().__init__()
        self.file_service = file_service
        self.interaction = interaction
        self.job_fetcher = JobFetcher(file_service)

    def execute(self):
        json_files = self.file_service.list_files("input", ".json")
        input_file = self.interaction.select_from_list("Select a job list file to process:", json_files)
        
        if not input_file:
            print("No file selected. Aborting.")
            return

        output_file = input_file.replace(".json", "_details.json")
        
        self.job_fetcher.fetch_job_details(input_file, output_file)

    def validate(self) -> bool:
        # For now, we assume validation passes if required directories exist.
        # More complex validation can be added here.
        return True

    def cleanup(self):
        # Ensure LinkedIn fetcher resources are cleaned up
        if hasattr(self.job_fetcher, 'linkedin_fetcher'):
            self.job_fetcher.linkedin_fetcher.cleanup()

if __name__ == '__main__':
    from job_seeker_cli.services.path_manager import PathManager
    
    path_manager = PathManager()
    file_service = FileService(path_manager)
    
    # To run this, you would need a file in the data/input directory
    # For example, create a dummy file:
    # file_service.write_json("input", "dummy_jobs.json", [{"recommendedJobLink": "http://example.com"}])
    
    workflow = JobScrapingWorkflow(file_service)
    workflow.run()
