import os
from job_seeker_cli.workflows.base_workflow import BaseWorkflow
from job_seeker_cli.services.file_service import FileService
from job_seeker_cli.services.api_client import ApiClient
from job_seeker_cli.scripts.letter_generator import LetterGenerator

from job_seeker_cli.ui.user_interaction import UserInteraction

class CoverLetterWorkflow(BaseWorkflow):
    def __init__(self, file_service: FileService, api_client: ApiClient, interaction: UserInteraction):
        super().__init__()
        self.file_service = file_service
        self.interaction = interaction
        self.letter_generator = LetterGenerator(file_service, api_client)

    def execute(self):
        target_files = self.file_service.list_files("target_jobs", ".json")
        jobs_file = self.interaction.select_from_list("Select a target jobs file:", target_files)
        if not jobs_file:
            print("No jobs file selected. Aborting.")
            return

        personal_files = self.file_service.list_files("personal", ".md")
        resume_file = self.interaction.select_from_list("Select your resume file:", personal_files)
        if not resume_file:
            print("No resume file selected. Aborting.")
            return
            
        prompt_file = self.interaction.select_from_list("Select a prompt template file:", personal_files)
        if not prompt_file:
            print("No prompt file selected. Aborting.")
            return

        guide_file = self.interaction.select_from_list("Select a guide file:", personal_files)
        if not guide_file:
            print("No guide file selected. Aborting.")
            return

        self.letter_generator.generate_letters(jobs_file, resume_file, prompt_file, guide_file)

    def validate(self) -> bool:
        return True

    def cleanup(self):
        pass

if __name__ == '__main__':
    from job_seeker_cli.services.path_manager import PathManager
    from job_seeker_cli.ui.user_interaction import UserInteraction
    
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        raise RuntimeError('DEEPSEEK_API_KEY is not set.')

    path_manager = PathManager()
    file_service = FileService(path_manager)
    api_client = ApiClient(api_key=api_key, api_url='https://api.deepseek.com/chat/completions')
    interaction = UserInteraction()
    
    # Dummy files for testing:
    # file_service.write_json("target_jobs", "dummy_targets.json", [{"jobDetails": "Some details"}])
    # file_service.write_file("personal", "dummy_resume.md", "My resume")
    # file_service.write_file("personal", "dummy_prompt.md", "My prompt")

    workflow = CoverLetterWorkflow(file_service, api_client, interaction)
    workflow.run()
