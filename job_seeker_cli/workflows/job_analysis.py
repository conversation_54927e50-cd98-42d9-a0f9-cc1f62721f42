import os
from datetime import datetime
from job_seeker_cli.workflows.base_workflow import BaseWorkflow
from job_seeker_cli.services.file_service import FileService
from job_seeker_cli.services.api_client import ApiClient
from job_seeker_cli.scripts.job_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON>

from job_seeker_cli.ui.user_interaction import UserInteraction

class JobAnalysisWorkflow(BaseWorkflow):
    def __init__(self, file_service: FileService, api_client: ApiClient, interaction: UserInteraction):
        super().__init__()
        self.file_service = file_service
        self.interaction = interaction
        self.job_analyzer = JobAnalyzer(file_service, api_client)

    def execute(self):
        processed_files = self.file_service.list_files("processed", ".json")
        input_file = self.interaction.select_from_list("Select a processed job file to analyze:", processed_files)
        if not input_file:
            print("No file selected. Aborting.")
            return

        resume_files = self.file_service.list_files("personal", ".md")
        resume_file = self.interaction.select_from_list("Select your resume file:", resume_files)
        if not resume_file:
            print("No resume file selected. Aborting.")
            return

        today = datetime.now().strftime('%m%d')
        output_file = f'target_jobs_{today}.json'
        
        self.job_analyzer.analyze_jobs(input_file, resume_file, output_file)

    def validate(self) -> bool:
        return True

    def cleanup(self):
        pass

if __name__ == '__main__':
    from job_seeker_cli.services.path_manager import PathManager
    
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        raise RuntimeError('DEEPSEEK_API_KEY is not set.')

    path_manager = PathManager()
    file_service = FileService(path_manager)
    api_client = ApiClient(api_key=api_key, api_url='https://api.deepseek.com/chat/completions')
    
    # Dummy files for testing:
    # file_service.write_json("processed", "dummy_processed.json", [{"jobDetails": "Some details"}])
    # file_service.write_file("personal", "dummy_resume.md", "My resume")

    workflow = JobAnalysisWorkflow(file_service, api_client)
    workflow.run()
