## Commands

### Installation
`pip install -r requirements.txt`

### Running the Application
- Main workflow: `python menu_system.py`

> **Note:** When running in a non-interactive environment (like সময়ের মধ্যে), you may encounter an `EOFError`. This is expected as the application requires user input. Running the script in a standard terminal will resolve this issue.

### Testing
- Run all tests: `pytest`
- Run tests in a specific file: `pytest tests/unit/test_data_manager.py`
- Run a specific test: `pytest tests/unit/test_data_manager.py::TestClassName::test_method_name`

## Code Style
- **Formatting**: Follows PEP 8. Use a linter like `flake8` or `pylint` for checking.
- **Imports**: Use absolute imports. Group imports in the following order: standard library, third-party packages, and then local application imports.
- **Naming**: Use `snake_case` for variables and functions, and `PascalCase` for classes.
- **Types**: Use type hints for all function signatures.
- **Error Handling**: Use try-except blocks for operations that can fail (e.g., file I/O, network requests).
- **Docstrings**: Use docstrings to explain the purpose of modules, classes, and functions.
