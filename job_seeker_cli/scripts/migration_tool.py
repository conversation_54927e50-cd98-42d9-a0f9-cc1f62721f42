import os
import shutil
from pathlib import Path

from job_seeker_cli.services.path_manager import PathManager

class MigrationTool:
    def __init__(self, root_dir: Path):
        self.root_dir = root_dir
        self.new_path_manager = PathManager(base_dir=self.root_dir / "job_seeker_cli")
        
        # Define old paths
        self.old_job_data_path = self.root_dir / "job-data"
        self.old_personal_info_path = self.root_dir / "personal_info"
        self.old_applying_job_path = self.root_dir / "applying-job"

    def migrate(self):
        """
        Performs the migration of data from the old structure to the new one.
        """
        print("Starting data migration...")

        # Migrate job data
        self._migrate_directory(self.old_job_data_path, self.new_path_manager.get_path("data"))
        
        # Migrate personal info
        self._migrate_directory(self.old_personal_info_path, self.new_path_manager.get_path("personal"))

        # Migrate cover letters
        self._migrate_directory(self.old_applying_job_path, self.new_path_manager.get_path("cover_letters"))

        print("\\nMigration complete. Please review the new directory structure.")
        print("It is recommended to back up and then remove the old directories.")

    def _migrate_directory(self, old_dir: Path, new_dir: Path):
        """
        Helper to migrate files from an old directory to a new one.
        """
        if not old_dir.exists():
            print(f"Skipping '{old_dir.name}': Directory not found.")
            return

        print(f"\\nMigrating '{old_dir.name}' to '{new_dir}'...")
        new_dir.mkdir(exist_ok=True)

        for item in os.listdir(old_dir):
            old_item_path = old_dir / item
            new_item_path = new_dir / item
            
            if old_item_path.is_file():
                if new_item_path.exists():
                    print(f"  - Skipping '{item}': File already exists in destination.")
                else:
                    shutil.copy2(old_item_path, new_item_path)
                    print(f"  - Copied '{item}'")

if __name__ == '__main__':
    # This tool should be run from the root of the project.
    project_root = Path(__file__).parent.parent.parent
    
    # Create dummy old directories and files for testing
    (project_root / "job-data").mkdir(exist_ok=True)
    (project_root / "personal_info").mkdir(exist_ok=True)
    (project_root / "job-data" / "test_job.json").touch()
    (project_root / "personal_info" / "test_cv.md").touch()

    tool = MigrationTool(project_root)
    tool.migrate()

    # Clean up dummy files
    os.remove(project_root / "job-data" / "test_job.json")
    os.remove(project_root / "personal_info" / "test_cv.md")
    os.rmdir(project_root / "job-data")
    os.rmdir(project_root / "personal_info")
