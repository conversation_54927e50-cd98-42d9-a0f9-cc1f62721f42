# /Users/<USER>/Desktop/aus_job/job_seeker_cli/scripts/seek_utils.py
# Seek job details scraper: Handles Seek.com.au job page content extraction
# This file contains the existing Seek scraping logic extracted for better organization and consistency

import json
import requests
from bs4 import BeautifulSoup
import re
import logging
from typing import Optional

# Seek specific headers
SEEK_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                  'AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/********* Safari/537.36',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Referer': 'https://www.seek.com.au',
    'Origin': 'https://www.seek.com.au'
}

def clean_unicode_text(text: str) -> str:
    """Clean text of invalid unicode characters."""
    if not isinstance(text, str):
        return text
    return re.sub(r'[\ud800-\udfff]', '', text)

class SeekFetcher:
    """Seek job details scraper"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def fetch_job_details(self, url: str) -> str:
        """
        Fetch Seek job details
        
        Args:
            url: Seek job link
            
        Returns:
            Job details text
        """
        try:
            self.logger.info(f"Fetching Seek job: {url}")
            response = requests.get(url, headers=SEEK_HEADERS, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Try primary selector for job details
            detail_div = soup.find(attrs={'data-automation': 'jobAdDetails'})
            
            details_text = ''
            if detail_div:
                details_text = detail_div.get_text(separator='\\n').strip()
            else:
                # Fallback to JSON-LD structured data
                ld_script = soup.find('script', type='application/ld+json')
                if ld_script:
                    try:
                        ld = json.loads(ld_script.string)
                        details_text = ld.get('description', '').strip()
                    except json.JSONDecodeError:
                        self.logger.warning(f"Could not parse JSON-LD: {url}")
                else:
                    self.logger.warning(f"No job details found: {url}")
            
            return clean_unicode_text(details_text)
            
        except requests.RequestException as e:
            self.logger.error(f"Failed to fetch Seek job {url}: {e}")
            return f"Failed to fetch Seek job details: {str(e)}"
        except Exception as e:
            self.logger.error(f"Unexpected error fetching Seek job {url}: {e}")
            return f"Unexpected error: {str(e)}"
