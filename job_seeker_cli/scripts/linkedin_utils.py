# /Users/<USER>/Desktop/aus_job/job_seeker_cli/scripts/linkedin_utils.py
# LinkedIn job details scraper: Uses Selenium and Requests dual strategy
# This file handles LinkedIn job page content extraction with anti-scraping and error handling mechanisms

import time
import logging
from typing import Optional
from random import uniform
import requests
from bs4 import BeautifulSoup
import re

# Try to import Selenium
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

# LinkedIn specific selectors
LINKEDIN_SELECTORS = [
    '.mt4',                          # Primary selector
    '.job-description',              # Backup selector 1
    '.description__text',            # Backup selector 2
    '.show-more-less-html__markup'   # Backup selector 3
]

# Request headers
LINKEDIN_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Connection': 'keep-alive',
    'Cache-Control': 'max-age=0'
}

def clean_html_text(html_content: str) -> str:
    """Clean HTML content and extract text"""
    # Remove HTML comments
    html_content = re.sub(r'<!--.*?-->', '', html_content, flags=re.DOTALL)
    
    # Create BeautifulSoup object
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Get text content
    text = soup.get_text(separator=' ', strip=True)
    
    # Clean extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

class LinkedInFetcher:
    """LinkedIn job details scraper"""
    
    def __init__(self):
        self.driver = None
        self.logger = logging.getLogger(__name__)
    
    def fetch_job_details(self, url: str) -> str:
        """
        Fetch LinkedIn job details
        
        Args:
            url: LinkedIn job link
            
        Returns:
            Job details text
        """
        # Strategy 1: Try Selenium
        if SELENIUM_AVAILABLE:
            details = self._fetch_with_selenium(url)
            if details:
                return details
        
        # Strategy 2: Backup Requests
        details = self._fetch_with_requests(url)
        if details:
            return details
        
        # Both failed
        return "Failed to fetch job details. LinkedIn may require login."
    
    def _setup_selenium_driver(self):
        """Setup Selenium WebDriver"""
        if not SELENIUM_AVAILABLE:
            return None
        
        try:
            options = Options()
            options.add_argument("--headless")
            options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-notifications")
            options.add_argument(f"user-agent={LINKEDIN_HEADERS['User-Agent']}")
            
            self.driver = webdriver.Chrome(options=options)
            return self.driver
        except Exception as e:
            self.logger.error(f"Failed to setup Selenium driver: {e}")
            return None
    
    def _fetch_with_selenium(self, url: str) -> Optional[str]:
        """Fetch using Selenium"""
        try:
            if not self.driver:
                self.driver = self._setup_selenium_driver()
            
            if not self.driver:
                return None
            
            self.logger.info(f"Fetching with Selenium: {url}")
            self.driver.get(url)
            
            # Wait for page load
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Additional wait for JavaScript execution
            time.sleep(3)
            
            # Try to find job details
            for selector in LINKEDIN_SELECTORS:
                try:
                    if selector.startswith('.'):
                        element = self.driver.find_element(By.CLASS_NAME, selector[1:])
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    html_content = element.get_attribute('outerHTML')
                    return clean_html_text(html_content)
                except NoSuchElementException:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"Selenium fetch failed: {e}")
            return None
    
    def _fetch_with_requests(self, url: str) -> Optional[str]:
        """Fetch using Requests"""
        try:
            self.logger.info(f"Fetching with requests: {url}")
            response = requests.get(url, headers=LINKEDIN_HEADERS, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Try various selectors
            for selector in LINKEDIN_SELECTORS:
                element = soup.select_one(selector)
                if element:
                    return clean_html_text(str(element))
            
            return None
            
        except Exception as e:
            self.logger.error(f"Requests fetch failed: {e}")
            return None
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("Selenium driver closed")
            except Exception as e:
                self.logger.error(f"Error closing driver: {e}")
    
    def add_random_delay(self):
        """Add random delay to prevent blocking"""
        delay = uniform(3.0, 7.0)
        self.logger.info(f"Waiting {delay:.2f} seconds...")
        time.sleep(delay)
