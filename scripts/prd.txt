<context>
# Overview  
Job Seeker CLI is an automation tool that transforms the existing `/auto-mation` scripts into a unified, user-friendly command-line interface. The product solves the problem of scattered job-seeking automation scripts by providing a centralized, interactive CLI that streamlines the entire job application process from job data collection to cover letter generation. It's designed for job seekers who want to automate their application workflow while maintaining full control over the process.

# Core Features  
## 1. Interactive Menu System
- Provides a modern, intuitive command-line interface with numbered menu options
- Displays real-time status and progress indicators
- Supports both interactive and direct command modes
- Important: Creates a professional user experience that rivals GUI applications

## 2. Intelligent Job Data Processing
- Automatically scans and lists available JSON job data files with metadata (size, date)
- Supports multiple data sources (LinkedIn, Seek.com.au, etc.)
- Provides file validation and preview capabilities
- Why it's important: Eliminates manual file handling and reduces user errors

## 3. Multi-Source Job Detail Fetching
- Universal job scraper for Seek.com.au and other job sites
- Specialized LinkedIn scraper with enhanced capabilities
- Automatic source detection and optimal scraper selection
- How it works: Wraps existing Python scripts with enhanced error handling and progress tracking

## 4. AI-Powered Job Matching
- Uses DeepSeek API to analyze job fit against user's resume
- Generates match scores (0-100) with detailed evaluation
- Filters and prioritizes high-matching positions (80+ score)
- Creates structured output for downstream processing

## 5. Automated Cover Letter Generation
- Generates personalized cover letters based on job analysis
- Uses existing templates and prompts with AI enhancement
- Automatically organizes output files with proper naming conventions
- Supports batch processing for multiple applications

# User Experience  
## User Personas
- **Primary**: Mid-level professionals actively job hunting with technical comfort
- **Secondary**: Career changers seeking automation to handle application volume
- **Tertiary**: Recruiters or career consultants managing multiple client applications

## Key User Flows
1. **First-time Setup**: User runs CLI → sees welcome screen → configures API keys → ready to use
2. **Daily Usage**: Launch CLI → select job data file → choose workflow → monitor progress → review results
3. **Maintenance**: Update configurations → view statistics → manage generated files

## UI/UX Considerations
- Rich terminal interface with colors, tables, and progress bars
- Clear error messages with suggested solutions
- Consistent navigation patterns across all functions
- Support for both guided (interactive) and expert (direct command) usage
</context>

<PRD>
# Technical Architecture  
## System Components
- **CLI Framework**: Click + Rich for modern terminal interface
- **Core Engine**: Python wrapper around existing automation scripts
- **Configuration Layer**: YAML-based settings with CLI override capabilities
- **Data Pipeline**: JSON file processing with validation and transformation
- **External APIs**: DeepSeek integration for AI analysis

## Data Models
```python
# Job Data Structure
{
  "jobAdvertiser": str,
  "jobTitle": str, 
  "recommendedJobLink": str,
  "jobDetails": str,  # Scraped content
  "match_score": int,  # AI-generated 0-100
  "evaluation": str    # AI reasoning
}

# Configuration Structure
{
  "app": {"name", "version"},
  "paths": {"job_data_dir", "output_dir", "scripts_dir"},
  "api": {"deepseek": {"timeout", "max_retries"}},
  "defaults": {"fetch_method", "match_threshold"}
}
```

## APIs and Integrations
- DeepSeek Chat API for job matching analysis
- File system integration for job data and output management
- Subprocess integration with existing Python automation scripts
- Rich library for terminal UI rendering

## Infrastructure Requirements
- Python 3.8+ runtime environment
- Internet connectivity for AI API calls and job scraping
- Local file system access for data storage
- Cross-platform compatibility (Windows, macOS, Linux)

# Development Roadmap  
## Phase 1: Core CLI Foundation (MVP)
- Basic Click CLI structure with interactive menu
- File selection interface with Rich tables
- Configuration management system
- Script wrapper for existing automation tools
- **MVP Goal**: Users can run existing workflows through a friendly interface

## Phase 2: Enhanced User Experience
- Progress indicators and real-time status updates
- Comprehensive error handling and recovery
- Input validation and data verification
- Help system and command documentation
- **Goal**: Professional-grade CLI experience with robust error handling

## Phase 3: Advanced Features
- Statistics dashboard for job application tracking
- Configuration management through CLI commands
- Batch processing capabilities
- Plugin architecture for extensibility
- **Goal**: Feature-complete solution with advanced workflow management

## Phase 4: Performance and Polish
- Performance optimization for large datasets
- Comprehensive test coverage
- Installation packaging and distribution
- User documentation and tutorials
- **Goal**: Production-ready tool suitable for wide distribution

# Logical Dependency Chain
## Foundation Layer (Build First)
1. **CLI Framework Setup**: Essential for all user interactions
2. **Configuration System**: Required by all other components
3. **File Management Utilities**: Needed for data processing workflows

## Core Functionality Layer
4. **Script Wrapper System**: Enables reuse of existing automation
5. **Interactive Menu System**: Primary user interface
6. **Basic Error Handling**: Critical for user experience

## Enhancement Layer  
7. **Progress Indicators**: Improves user experience during long operations
8. **Statistics and Reporting**: Provides value-added insights
9. **Advanced Configuration**: Enables power-user customization

## Polish Layer
10. **Comprehensive Testing**: Ensures reliability
11. **Documentation**: Enables adoption
12. **Packaging**: Enables distribution

# Risks and Mitigations  
## Technical Challenges
- **Risk**: Existing script integration complexity
- **Mitigation**: Thorough testing of subprocess calls and parameter passing

- **Risk**: Cross-platform compatibility issues
- **Mitigation**: Use pathlib for paths, test on multiple OS platforms

- **Risk**: API rate limiting and failures
- **Mitigation**: Implement retry logic, fallback strategies, and clear error reporting

## MVP Definition and Buildability
- **Risk**: Over-engineering the initial version
- **Mitigation**: Start with simple script wrapping, add features incrementally

- **Risk**: User adoption barriers
- **Mitigation**: Maintain backward compatibility with existing workflow

## Resource Constraints
- **Risk**: Development time estimation
- **Mitigation**: Prioritize core functionality over polish features

- **Risk**: External API dependencies
- **Mitigation**: Design modular architecture allowing API provider switching

# Appendix  
## Research Findings
- Current workflow analysis shows 5 distinct automation scripts
- Job data files range from 3MB to 200MB requiring efficient processing
- User currently manages 15+ job applications weekly
- Success rate improvement target: 25% through better targeting

## Technical Specifications
- **Dependencies**: click>=8.0.0, rich>=13.0.0, pyyaml>=6.0
- **Performance Targets**: <1s CLI response, <3s file loading
- **File Support**: JSON job data files up to 50MB
- **API Requirements**: DeepSeek API key for matching analysis

## Success Metrics
- **User Experience**: <5% error rate, positive user feedback
- **Performance**: All operations complete within target timeframes  
- **Functionality**: 100% feature parity with existing scripts
- **Adoption**: Tool replaces manual script execution entirely
</PRD> 