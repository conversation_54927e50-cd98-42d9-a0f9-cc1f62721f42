// /Users/<USER>/Desktop/aus_job/chrome_extension/popup.js
// 扩展弹出窗口的主要脚本，负责检测当前网站类型并触发爬取操作
// 此文件的目的是处理用户界面交互和显示爬取结果信息

// 当弹出窗口加载时，检测当前网站类型
document.addEventListener('DOMContentLoaded', function() {
  detectSiteType();
});

// 爬取按钮点击事件
document.getElementById('extractBtn').onclick = function() {
  document.getElementById('status').textContent = '正在爬取...';
  document.getElementById('count-info').textContent = '';
  
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    // 先尝试获取页面中已有的数据
    chrome.scripting.executeScript({
      target: {tabId: tabs[0].id},
      func: () => {
        return {
          data: window.__jobListExtractedData || null,
          url: window.location.href
        };
      }
    }, function(results) {
      let result = results && results[0] && results[0].result;
      let data = result ? result.data : null;
      let url = result ? result.url : '';
      
      // 更新网站类型标识
      updateSiteBadge(url);
      
      if (!data) {
        document.getElementById('status').textContent = '未检测到数据，尝试注入脚本...';
        // 发送消息给content.js执行爬取
        chrome.tabs.sendMessage(tabs[0].id, {action: 'extract_joblist'}, function(response) {
          if (response && response.data) {
            saveData(response.data);
          } else {
            document.getElementById('status').textContent = '爬取失败，请确认页面结构或刷新页面重试。';
          }
        });
      } else {
        saveData(data);
      }
    });
  });
};

/**
 * 检测当前网站类型并更新UI
 */
function detectSiteType() {
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (tabs[0] && tabs[0].url) {
      updateSiteBadge(tabs[0].url);
    }
  });
}

/**
 * 根据URL更新网站类型标识
 */
function updateSiteBadge(url) {
  const siteTypeElement = document.getElementById('site-type');
  let badgeHTML = '';
  
  if (url.includes('linkedin.com')) {
    badgeHTML = '<span class="site-badge linkedin">LinkedIn</span>';
  } else if (url.includes('seek.com')) {
    badgeHTML = '<span class="site-badge seek">Seek</span>';
  } else {
    badgeHTML = '<span class="site-badge unknown">不支持的网站</span>';
  }
  
  siteTypeElement.innerHTML = badgeHTML;
}

/**
 * 保存爬取的数据
 */
function saveData(data) {
  if (!data || data.length === 0) {
    document.getElementById('status').textContent = '未找到职位数据';
    return;
  }
  
  // 显示找到的职位数量
  document.getElementById('count-info').textContent = `找到 ${data.length} 个职位`;
  
  // 创建JSON Blob并触发下载
  const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
  const url = URL.createObjectURL(blob);
  
  // 发送消息给background.js处理下载
  chrome.runtime.sendMessage({action: 'download', url: url}, function(resp) {
    document.getElementById('status').textContent = '请在弹出的窗口选择保存路径';
  });
}
