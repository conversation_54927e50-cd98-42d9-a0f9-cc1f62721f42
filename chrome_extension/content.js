// /Users/<USER>/Desktop/aus_job/chrome_extension/content.js
// 内容脚本：注入页面，提取职位信息，支持Seek和LinkedIn
// 此文件的目的是从不同招聘网站提取职位信息，目前支持Seek和LinkedIn

/**
 * 检测当前网站类型并调用相应的提取函数
 */
function extractJobCards() {
  const currentUrl = window.location.href;
  let jobs = [];
  
  // 根据URL判断当前网站类型
  if (currentUrl.includes('linkedin.com')) {
    jobs = extractLinkedInJobs();
  } else if (currentUrl.includes('seek.com')) {
    jobs = extractSeekJobs();
  } else {
    console.log('当前网站不支持职位提取');
  }
  
  // 将提取的数据存储在全局变量中，以便popup.js访问
  window.__jobListExtractedData = jobs;
  return jobs;
}

/**
 * 提取Seek网站的职位信息
 */
function extractSeekJobs() {
  // 获取所有包含工作卡片的元素
  const jobCards = document.querySelectorAll('div[data-search-sol-meta]');
  // 存储提取的信息
  const jobs = [];
  
  // 遍历每个工作卡片
  jobCards.forEach(card => {
    // 提取职位名称
    const jobTitle = card.querySelector('[data-automation="jobTitle"]')?.textContent || '';
    // 提取公司名称
    const jobAdvertiser = card.querySelector('[data-automation="jobAdvertiser"]')?.textContent || '';
    // 提取推荐职位链接
    const recommendedJobLink = card.querySelector('[data-automation^="recommendedJobLink"]')?.href || '';
    
    // 将提取的信息添加到数组
    jobs.push({
      jobTitle,
      jobAdvertiser,
      recommendedJobLink
    });
  });
  
  return jobs;
}

/**
 * 提取LinkedIn网站的职位信息
 * 使用更新后的linkedin_extract_job.js的逻辑，优化了选择器和提取方法
 */
function extractLinkedInJobs() {
  const jobListings = [];
  
  // 选择所有职位卡片列表项
  const jobCards = document.querySelectorAll('li.scaffold-layout__list-item');
  
  // 处理每个职位卡片
  jobCards.forEach(card => {
    // 提取职位ID以验证是否为有效的职位卡片
    const jobId = card.getAttribute('data-occludable-job-id');
    if (!jobId) return; // 如果不是有效的职位卡片则跳过
    
    // 在列表项中查找职位容器
    const jobContainer = card.querySelector('div.job-card-container');
    if (!jobContainer) return;
    
    let jobTitle = '未知职位';
    let company = '未知公司';
    let jobLink = '#';
    
    // 提取职位标题
    const titleElement = jobContainer.querySelector('a.job-card-list__title--link');
    if (titleElement) {
      // 获取链接作为推荐职位链接
      jobLink = titleElement.href;
      
      // 从strong元素或aria-label属性中提取职位标题
      const titleStrongElement = titleElement.querySelector('strong');
      if (titleStrongElement && titleStrongElement.textContent.trim()) {
        jobTitle = titleStrongElement.textContent.trim();
      } else {
        // 备选方案：从aria-label获取，并去除" with verification"等多余文本
        const ariaLabel = titleElement.getAttribute('aria-label');
        if (ariaLabel) {
          jobTitle = ariaLabel.replace(/ with verification$/i, '').trim();
        }
      }
    }
    
    // 提取公司名称
    const companyElement = jobContainer.querySelector('.artdeco-entity-lockup__subtitle span');
    if (companyElement) {
      company = companyElement.textContent.trim();
    }
    
    // 将提取的信息添加到数组，保持与原始输出格式一致
    jobListings.push({
      jobTitle: jobTitle,
      jobAdvertiser: company,
      recommendedJobLink: jobLink
    });
  });

  // 尝试滚动页面来加载更多结果
  // 滚动到页面底部来触发加载更多结果
  window.scrollTo(0, document.body.scrollHeight);
  
  return jobListings;
}

// 监听来自popup.js的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'extract_joblist') {
    const data = extractJobCards();
    sendResponse({data});
  }
});
