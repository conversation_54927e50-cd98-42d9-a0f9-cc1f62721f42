# FourQuarters Recruitment

statuts:
- saved
- waiting
- interviewing
- done

## info
{
    "jobTitle": "Digital BA",
    "jobAdvertiser": "FourQuarters Recruitment",
    "recommendedJobLink": "https://www.seek.com.au/job/84167477?ref=recom-homepage-genai&pos=36#sol=fd8e5ca343ffb579ccbf7b436dd94fcbb5a26955",
    "jobDetails": "Our client is a large organisation who require a strong technical BA who can convey customer needs to the technical team, whilst coming up with inventive solutions, process mapping, and documentation to support SDLC\nResponsibilities\n \n \nMust be a strong BA who is able to understand the client requirements, document the technical and functional requirements to meet the customer demands.\n \nHave a strong customer-focused background and can drive delivery of products to the client.\n \nExperience mapping requirements into product epics and features as well as creating artifacts, gap analysis, and process flow diagrams.\n \nWork with Product Managers to provide content to include in external-facing product documentation describing features, business rules, and use cases\n \nSupport the creation of UAT test cases with the consideration of client acceptance scenarios and conduct internal UAT of delivered features\n \nWork closely with the product team to understand the product lifecycle and to use that to translate requirements to the technical team.\n \nAble to get into detail in terms of technical solutions to influence the best options for functional changes that still meet business needs but also meet strict project schedule.\n \n \nQualifications & Experience\n \n \n5+ years BA experience \n \nIdeally experience in Digital Software for 5+ years \n \nHave experience working with UX/UI designers\n \nExperience creating detailed functional specifications for Digital software products (5 years)\n \nExperience in whiteboarding, building products from scratch, and using data-driven decisions to drive future roadmaps",
    "match_score": 85,
    "evaluation": "Strong technical BA fit with deep product experience in AI/IoT, requirements translation, process mapping, and cross-functional collaboration. Limited explicit SDLC/UAT documentation focus."
}

## status:

## coverletter:
Dear Hiring Manager,

As a Technical Product Leader with 9 years of experience in AI and IoT solutions, I am excited to apply for the Business Analyst role. My background aligns closely with your requirements, particularly in translating complex client needs into technical deliverables, process mapping, and supporting product lifecycle delivery.

At Alibaba, I led cross-functional teams to define and document functional and technical requirements for digital products, including smart cockpit solutions adopted by 200,000 vehicles and an AI hybrid service desk serving over 50 global enterprises. You can view these and other projects on my portfolio at [https://jaysean1.github.io/](https://jaysean1.github.io/). I have extensive experience creating epics, user stories, process flow diagrams, and conducting gap analysis — all while maintaining a strong customer focus.

I hold a subclass 500 visa with no work restrictions and will be relocating to Sydney in June. I look forward to discussing how my skills can support your team’s goals.

Kind regards,  
Sui Qian

## Generated with: qwen