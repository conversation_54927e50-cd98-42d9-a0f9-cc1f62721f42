# Apate.Ai Pty Limited

statuts:
- saved
- waiting
- interviewing
- done

## info
{
    "jobAdvertiser": "Apate.Ai Pty Limited",
    "jobTitle": "Agile Project Manager – Software Development",
    "recommendedJobLink": "https://www.seek.com.au/job/83941713?ref=recom-homepage-genai&pos=30#sol=95fee5f47adff5eb5d04eba3064d89059aed4b1d",
    "jobDetails": "About Us\nApate.AI is an innovative cybersecurity start-up that revolutionises scam prevention using advanced conversational AI. Our flagship products Apate Voice, Apate Text, and Apate Insights deploy lifelike AI personas to engage scammers, waste their time, and extract critical intelligence on their tactics.\nA spin-out from Macquarie University, we partner with telcos, financial institutions, and law enforcement to proactively disrupt scam operations and dismantle fraudulent business models globally. As we expand, we’re looking for mission-driven team members who thrive in agile, delivery-focused environments and want to make real-world impact.\nAbout the Role\nWe are seeking a delivery-focused Agile Project Manager to join our team on a permanent, full time basis. This role is ideal for someone who excels at leading agile software teams, driving project outcomes, and balancing multiple streams of delivery. You’ll coordinate sprint execution, customer deployments, and support technical go-to-market initiatives — all in a fast-paced, start-up environment where delivery drives impact.\nResponsibilities\nAgile Software Delivery \n•\tFacilitate agile ceremonies including sprint planning, daily stand-ups, reviews, and retrospectives\n•\tCollaborate with Product Owners to maintain and prioritise the backlog\n•\tMonitor sprint progress and manage blockers, risks, and cross-team dependencies\n•\tChampion continuous improvement, delivery velocity, and engineering best practices\n•\tFoster effective collaboration across engineering, QA, DevOps, and product teams\n•\tUse Jira, Confluence, and Azure DevOps to plan, track, and report delivery performance\nCustomer Project Delivery \n•\tManage delivery of customer-facing software projects\n•\tAlign internal resources and delivery plans with client expectations and SLAs\n•\tCoordinate technical implementation timelines, feedback loops, and support handovers\n•\tAct as the primary point of contact for delivery status, blockers, and escalations\nTechnical Support for Sales & Marketing \n•\tCollaborate with sales teams to scope and define technical solutions during pre-sales\n•\tCoordinate delivery inputs for RFPs, proposals, and client-facing collateral\n•\tSupport product demos and technical workshops for customers and partners\n•\tAlign go-to-market initiatives with engineering capacity and delivery cadence\nExperience\nRequired Experience\n•\t5+ years’ experience leading Agile software delivery projects\n•\tExperience managing full SDLC (from requirements to deployment)\n•\t⁠Familiarity with modern software architecture concepts (microservices, REST APIs, CI/CD pipelines)\n•\tS⁠trong understanding of DevOps pipelines – collaboration with development, QA, and release teams\n•\tProven ability to coordinate across cross-functional software teams\n•\tStrong grasp of Agile frameworks: Scrum, Kanban (SAFe a bonus)\n•\tExperience managing sprint cycles, backlogs, and delivery pipelines\n•\tExcellent stakeholder engagement and communication skills\n•\tProficiency with Jira, Confluence, Azure DevOps or similar tools\n•\tComfortable balancing shifting priorities and leading multiple concurrent workstreams\nPreferred Experience\n•\tAgile certifications (e.g., CSM, PMI-ACP, SAFe Agilist)\n•\tExperience in B2B SaaS or product-led tech organisations\n•\tExposure to cloud-based environments and DevOps practices\n•\tPrior experience working in high-growth start-ups or lean delivery teams\n•\tFamiliarity with customer success or technical pre-sales functions\nWhy Join Us?\n•\tBe part of a mission-driven team tackling global scam and fraud challenges\n•\tWork on technically interesting problems with modern frameworks and cloud platforms\n•\tFlexible work arrangements (hybrid or remote)\n•\tOpportunity to contribute to real-world impact in collaboration with industry and law enforcement\n•\tZero bureaucracy, real impact\n•\tWork directly with founders and researchers from day one\nHow to Apply\nSend a short note about why you’re interested in this role, along with your CV or LinkedIn profile, to \n[email protected]\n by 11:59pm, 18 May 2025. Shortlisted candidates will be contacted for interview.",
    "match_score": 85,
    "evaluation": "Strong technical product leadership in AI and IoT aligns well with Apate.AI’s agile, delivery-focused environment. Extensive cross-functional team coordination and stakeholder management experience."
}

## status:

## coverletter:
Dear Hiring Manager,

With 9 years of experience as a Technical Product Leader specializing in AI and IoT solutions, I’m excited to transition into an Agile Project Manager role at Apate.AI. My expertise in driving agile software delivery projects and managing the full SDLC aligns closely with your needs. At Alibaba Cloud, I successfully led the development of in-vehicle AI voice assistants and cloud applications used by 200,000 devices, demonstrating my ability to coordinate cross-functional teams and deliver impactful solutions.

I will be arriving in Sydney in July and welcome you to explore my past product achievements on my personal website: https://jaysean1.github.io/. My experience in Scrum methodologies, stakeholder engagement, and DevOps practices positions me well for this role. I’m eager to contribute to Apate.AI’s mission of combating global scams through innovative technology.

Thank you for considering my application. I look forward to discussing how my skills can support your team.

Sincerely,  
Sui Qian