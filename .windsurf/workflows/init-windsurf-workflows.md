---
description: How to initialize a new project with core windsurf workflows
---

- Purpose: This workflow initializes a new project by automatically generating the following three core workflows. 
- All generated workflow files should be saved as Markdown (.md) files under the .windsurf/workflows directory.
- During the workflow generation process, do not modify any content of the following guideline in the prompt. Please keep it completely consistent.

Workflow One: [generate-windsurf-rules] desc: How to create project rules for a windsurf project
guideline:
* Generate appropriate windsurf-rules files in Markdown format based on the project context.
* The generated rules should be saved in the .windsurf/rules path.
* At minimum, include:   - project-structure-rules.md: Describes the project’s structure and its core purpose.
* Generate or update additional rules files as needed, such as:   - tech-stack-rules.md: Defines the technology stack and usage guidelines.   - css-rules.md: Specifies CSS naming conventions and styling guidelines.   - git-workflow-rules.md: Outlines Git branching and commit standards.   - testing-rules.md: Details testing strategies and requirements.   - api-design-rules.md: Guides API structure and endpoint conventions.
* Only include rules files relevant to the project’s current needs. Avoid generating unnecessary files.
* If rules files already exist, update them based on the latest project requirements, modifying only the specified sections while preserving unchanged content.
* At the top of each rules file, add a Description (within 50 characters) explaining when the rule applies, e.g., "For web development workflows.”
* For the Activation Mode of each rule, please use MODEL DECISION, and fill in the Description within the rule.


Workflow Two: [generate-search-query] desc: How to summarize project issues and generate effective search queries
guideline: Take a deep breath. Carefully review the entire conversation and the codebase above. This is a project-related discussion. Your task is to generate a complete and actionable problem summary that can be used for search and analysis by external AI models such as ChatGPT, Claude, Grok, Gemini, etc.

The summary should include:
1. A concise summary of the current status of the project.
2. The main issues or blockers we've encountered.
3. The solutions we’ve already tried and what the outcomes were.
4. A clearly defined problem we still need to solve, and the format or type of answer we are hoping to receive (e.g., code examples, explanation, troubleshooting steps).
Make sure the final output is in English, focused, and actionable. Use technical terms if necessary.
EXAMPLE of your output as:
```
You are a senior programmer. I have encountered problems during the coding process. If these problems cannot be solved, it may cause huge economic losses. Therefore, I need you to provide solutions based on the following information：
* Project Summary: [Brief summary]
* Current Problems: [List of issues encountered]
* Tried Solutions & Outcomes: [What has been tried so far and what happened]
* Target Problem: [What specific problem we still need to solve right now]
* Expected Answer : [What kind of answer will best help us resolve the issue，be as specific as possible]
```

Workflow Three: [update-project-memory] desc: How to extract key updates and issues from the conversation and update memory
guideline: Take a deep breath. Carefully review the entire conversation above. Your task is to extract key points and important updates that should be stored into project memory. Focus on:
* Bugs mentioned and how they were fixed (if any)
* Corrections or clarifications provided by the user
* Important insights or discoveries you made
Then, summarize these findings clearly and briefly. Your response should help the user understand what has been captured.
Structure your response as:
* Bug Fixes: [What bugs were identified and how they were addressed]
* User Corrections: [Any clarifications, corrections, or emphasized points by the user]
* Discovered Insights: [Important facts or logic you noticed during the conversation]
* Memory Summary: [A clean summary that can be added to long-term memory]
Be concise, use bullet points, and avoid redundancy.
