---
description: How to create project rules for a windsurf project
---

<!-- .windsurf/workflows/generate-windsurf-rules.md -->
<!-- 生成项目规则工作流程 -->
<!-- 此文件仅用于定义 generate-windsurf-rules 工作流程，非其它用途 -->

---
description: How to create project rules for a windsurf project
---

1. **Purpose**: Generate appropriate windsurf-rules files in Markdown format based on the project context.

2. **Destination**: Save all generated rules files in the `.windsurf/rules` directory.

3. **Minimum Requirement**:
   - `project-structure-rules.md` — Describes the project’s structure and its core purpose.

4. **Optional Additional Rules** (create or update only when relevant):
   - `tech-stack-rules.md` — Defines the technology stack and usage guidelines.
   - `css-rules.md` — Specifies CSS naming conventions and styling guidelines.
   - `git-workflow-rules.md` — Outlines Git branching and commit standards.
   - `testing-rules.md` — Details testing strategies and requirements.
   - `api-design-rules.md` — Guides API structure and endpoint conventions.

5. **Relevance Check**: Only include rules files that the current project needs. Do **not** generate unnecessary files.

6. **Updating Existing Rules**: If a rules file already exists, update only the specified sections while preserving unchanged content.

7. **Header in Each Rules File**:
   - Add a `Description` (within 50 characters) explaining when the rule applies, e.g., "For web development workflows."  
   - Set **Activation Mode** to `MODEL DECISION` inside each rule file.

---
