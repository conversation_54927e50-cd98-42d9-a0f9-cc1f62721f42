<!-- .windsurf/workflows/update-project-memory.md -->
<!-- 定义 update-project-memory 工作流程 -->
<!-- 仅用于从对话中提取关键信息并更新项目记忆 -->

---
description: How to extract key updates and issues from the conversation and update memory
---

1. **Purpose**: Parse recent conversation to capture important updates, fixes, and insights, then store them into project memory.

2. **Preparation**:
   - Take a deep breath and carefully review the entire conversation above.

3. **Focus Areas**:
   - Bugs mentioned and how they were fixed (if any)
   - Corrections or clarifications provided by the user
   - Important insights or discoveries made during debugging or discussion

4. **Response Structure** (use bullet points, be concise, avoid redundancy):
   - `Bug Fixes:` [What bugs were identified and how they were addressed]
   - `User Corrections:` [Any clarifications, corrections, or emphasized points by the user]
   - `Discovered Insights:` [Important facts or logic you noticed during the conversation]
   - `Memory Summary:` [A clean summary that can be added to long-term memory]

5. **Style Guidelines**:
   - Keep it clear and brief.
   - Avoid repetition.

---
