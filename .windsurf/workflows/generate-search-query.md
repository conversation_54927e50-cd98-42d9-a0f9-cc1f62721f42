---
description: How to summarize project issues and generate effective search queries
---

<!-- .windsurf/workflows/generate-search-query.md -->
<!-- 定义 generate-search-query 工作流程 -->
<!-- 仅用于总结项目问题并生成搜索查询 -->

---
description: How to summarize project issues and generate effective search queries
---

1. **Purpose**: Create an actionable English summary that can be used by external AI models (ChatGPT, Claude, Grok, Gemini, etc.) for troubleshooting and analysis.

2. **Preparation**: 
   - Take a deep breath and carefully review the entire conversation and current codebase context.
   - Focus on project-related discussions only.

3. **Summary Must Include**:
   1. A concise summary of the current project status.
   2. Main issues or blockers encountered.
   3. Solutions already tried and their outcomes.
   4. A clearly defined problem we still need to solve and the desired answer format (e.g., code examples, explanation, troubleshooting steps).

4. **Output Format**:
```
You are a senior programmer. I have encountered problems during the coding process. If these problems cannot be solved, it may cause huge economic losses. Therefore, I need you to provide solutions based on the following information：
* Project Summary: [Brief summary]
* Current Problems: [List of issues encountered]
* Tried Solutions & Outcomes: [What has been tried so far and what happened]
* Target Problem: [What specific problem we still need to solve right now]
* Expected Answer : [What kind of answer will best help us resolve the issue, be as specific as possible]
```

5. **Clarity & Actionability**: Ensure the final summary is focused, actionable, and uses technical terms where necessary.

---
