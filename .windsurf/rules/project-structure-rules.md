---
trigger: always_on
---

# 📍 .windsurf/rules/project-structure-rules.md
# 本文件用于描述项目整体目录结构与核心目的
# 本文件**不**涉及技术栈细节、Git 流程或测试策略

---
Description: For Job-Match 自动化项目目录结构
Activation Mode: MODEL DECISION
---

## 目标  
为所有贡献者提供统一的项目结构规范，确保文件放置一致、命名清晰、职责单一。

## 目录规范  
| 顶层目录 | 职责 | 说明 |
| -------- | ---- | ---- |
| `.env` | 环境变量 | 仅存放本地机密，**不得**提交生产密钥 |
| `requirements.txt` | 依赖清单 | 锁定 Python 版本，禁止随意升级 |
| `auto-mation/` | 核心自动化脚本 | 单一职责，每个脚本仅完成一件事 |
| `chrome_extension/` | 浏览器插件代码 | 遵循 Manifest V3 |
| `job-data/` | 原始 / 处理后职位 JSON | 文件命名 `joblist_YYYYMMDD.json` |
| `personal_info/` | 简历及个人资料 | 私密数据，仅本地使用 |
| `applying-job/` | 生成的求职信 | 状态前缀 `(saved) / (waiting) / (interviewing)` |
| `doc/` | 设计与接口文档 | Markdown、图片等文档资料 |
| `.windsurf/` | Windsurf 配置 | 工作流、规则、记忆等元数据 |

## 文件命名  
1. 使用 `kebab-case` 或 `snake_case`，避免空格。  
2. Markdown 文件以 [.md](cci:7://file:///Users/<USER>/Desktop/aus_job/README.md:0:0-0:0) 结尾，脚本以语言后缀结尾。  
3. 求职信文件统一格式：`(status)Company Name.md`。

## 结构演进  
- **新增模块** 前先在 [README.md](cci:7://file:///Users/<USER>/Desktop/aus_job/README.md:0:0-0:0) 更新目录示例。  
- **重构目录** 需在 PR 中附迁移脚本 / 说明。  
- 禁止在非约定目录存放脚本或数据文件。

