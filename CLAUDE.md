# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Build & Run
1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
2. Run main workflow:
   ```bash
   python auto-mation/main.py --jobdata job-data/joblist_MMDD.json
   ```
3. Run individual modules:
   - Fetch job details:
     ```bash
     python auto-mation/fetch_job_details.py --jobdata job-data/joblist_MMDD.json
     ```
   - Analyze job match:
     ```bash
     python auto-mation/analyze_job_match.py --jobdata job-data/joblist_MMDD.json
     ```
   - Generate cover letter:
     ```bash
     python auto-mation/generate_coverletter.py
     ```

### Chrome Extension
1. Load extension:
   - Open `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select `chrome_extension` directory

## Architecture

The project is structured as follows:
- `auto-mation/`: Core automation scripts for job extraction, analysis, and cover letter generation.
- `chrome_extension/`: Chrome plugin for job listing extraction.
- `job-data/`: Stores job listings and match results (JSON files).
- `personal_info/`: Contains user resume for matching.
- `applying-job/`: Generated cover letters.
- `doc/`: Project documentation.

Key workflows:
1. Extract job listings via Chrome extension or `extract_job.js`.
2. Fetch and analyze job details via `fetch_job_details.py` and `analyze_job_match.py`.
3. Generate cover letters via `generate_coverletter.py`.

## Important Notes
- Ensure `.env` contains `DASHSCOPE_API_KEY`.
- Keep `personal_info/qiansui_cv.md` updated for accurate matching.
- Output files (`joblist_MMDD.json`, `target_job_MMDD.json`) are date-stamped to preserve history.