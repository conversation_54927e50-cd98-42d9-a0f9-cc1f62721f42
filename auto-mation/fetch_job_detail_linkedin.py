#!/usr/bin/env python
# /Users/<USER>/Desktop/aus_job/auto-mation/fetch_job_detail_linkedin.py
# This script fetches job details from LinkedIn job pages based on links in a JSON file
# It extracts content from the 'mt4' class and cleans HTML tags, updating the original JSON

import json
import requests
import os
from bs4 import BeautifulSoup
import re
import time
import logging
import argparse
from random import randint, uniform
import traceback

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("linkedin_scraper.log"),
        logging.StreamHandler()
    ]
)

# Try to import Selenium components
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
    logging.info("Selenium is available and imported successfully")
except ImportError:
    SELENIUM_AVAILABLE = False
    logging.warning("Selenium is not available. Install with: pip install selenium")

# Parse command line arguments for job data file path
def get_json_path():
    parser = argparse.ArgumentParser(description='Fetch LinkedIn job details')
    parser.add_argument('--jobdata', type=str, default='/Users/<USER>/Desktop/aus_job/job-data/joblist_0521.json', 
                       help='Job list JSON path')
    args, _ = parser.parse_known_args()
    if args.jobdata:
        # 确保相对路径转为绝对路径
        if not os.path.isabs(args.jobdata):
            return os.path.abspath(args.jobdata)
        return args.jobdata
    return '/Users/<USER>/Desktop/aus_job/job-data/joblist_0521.json'

# Constants
JSON_FILE_PATH = get_json_path()
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Cache-Control': 'max-age=0',
    'sec-ch-ua': '"Google Chrome";v="113", "Chromium";v="113"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1'
}

def load_json_data(file_path):
    """Load job data from JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return []
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON format in {file_path}")
        return []

def save_json_data(file_path, data):
    """Save updated job data to JSON file"""
    with open(file_path, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=2)
    print(f"Updated data saved to {file_path}")

def clean_html_text(html_content):
    """Clean HTML content and extract text"""
    # Remove HTML comments
    html_content = re.sub(r'<!--.*?-->', '', html_content, flags=re.DOTALL)
    
    # Create BeautifulSoup object
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Get text content
    text = soup.get_text(separator=' ', strip=True)
    
    # Clean up extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def setup_selenium_driver():
    """Set up and return a Selenium WebDriver instance"""
    if not SELENIUM_AVAILABLE:
        raise ImportError("Selenium is not available")
    
    options = Options()
    options.add_argument("--headless")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-popup-blocking")
    options.add_argument(f"user-agent={HEADERS['User-Agent']}")
    
    try:
        driver = webdriver.Chrome(options=options)
        return driver
    except Exception as e:
        logging.error(f"Failed to initialize Chrome WebDriver: {str(e)}")
        raise

def fetch_job_details_with_selenium(url, driver):
    """Fetch job details from LinkedIn job page using Selenium"""
    try:
        logging.info(f"Fetching with Selenium: {url}")
        driver.get(url)
        
        # Wait for the page to load (max 20 seconds)
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Give extra time for JavaScript to execute and content to load
        time.sleep(3)
        
        # Try to find the job details div
        try:
            job_details_div = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "mt4"))
            )
            
            # Get the HTML content of the div
            html_content = job_details_div.get_attribute('outerHTML')
            
            # Clean the HTML content and extract text
            job_details = clean_html_text(html_content)
            return job_details
        except (TimeoutException, NoSuchElementException) as e:
            logging.warning(f"Could not find job details div with Selenium: {str(e)}")
            
            # Try alternative selectors
            try:
                # Look for any job description container
                job_description = driver.find_element(By.CSS_SELECTOR, ".job-description, .description__text, .show-more-less-html__markup")
                html_content = job_description.get_attribute('outerHTML')
                job_details = clean_html_text(html_content)
                return job_details
            except NoSuchElementException:
                logging.warning("Could not find alternative job description elements")
                
                # Last resort: get the entire page content
                page_source = driver.page_source
                soup = BeautifulSoup(page_source, 'html.parser')
                
                # Look for common job description containers
                for selector in ['.mt4', '.job-description', '.description__text', '.show-more-less-html__markup']:
                    element = soup.select_one(selector)
                    if element:
                        return clean_html_text(str(element))
                
                return None
    except Exception as e:
        logging.error(f"Error fetching job details with Selenium from {url}: {str(e)}")
        logging.error(traceback.format_exc())
        return None

def fetch_job_details_with_requests(url):
    """Fetch job details from LinkedIn job page using requests"""
    try:
        logging.info(f"Fetching with requests: {url}")
        response = requests.get(url, headers=HEADERS, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find the div with class 'mt4' which contains job details
        job_details_div = soup.find('div', class_='mt4')
        
        if job_details_div:
            # Clean the HTML content and extract text
            job_details = clean_html_text(str(job_details_div))
            return job_details
        else:
            # Try alternative selectors
            for selector in ['.job-description', '.description__text', '.show-more-less-html__markup']:
                element = soup.select_one(selector)
                if element:
                    return clean_html_text(str(element))
            
            logging.warning(f"Could not find job details div in {url}")
            return None
    except requests.RequestException as e:
        logging.error(f"Error fetching job details from {url}: {str(e)}")
        return None

def main():
    """Main function to process job links and update JSON"""
    # Load existing job data
    job_data = load_json_data(JSON_FILE_PATH)
    
    if not job_data:
        logging.error("No job data found. Exiting.")
        return
    
    total_jobs = len(job_data)
    logging.info(f"Found {total_jobs} jobs in the JSON file. Starting to fetch details...")
    
    # Initialize Selenium driver
    try:
        driver = setup_selenium_driver()
        logging.info("Selenium WebDriver initialized successfully")
    except Exception as e:
        logging.error(f"Failed to initialize Selenium WebDriver: {str(e)}")
        logging.info("Falling back to requests-only mode")
        driver = None
    
    try:
        # Process each job
        for i, job in enumerate(job_data):
            job_link = job.get('recommendedJobLink')
            
            if not job_link:
                logging.warning(f"Warning: No job link found for job {i+1}")
                continue
            
            # Skip if job details already exist
            if 'jobAdDetails' in job and job['jobAdDetails']:
                logging.info(f"Job {i+1}/{total_jobs} already has details. Skipping.")
                continue
            
            logging.info(f"Processing job {i+1}/{total_jobs}: {job.get('jobTitle', 'Unknown Title')}")
            
            # Try to fetch job details using Selenium first
            job_details = None
            if driver:
                try:
                    job_details = fetch_job_details_with_selenium(job_link, driver)
                except Exception as e:
                    logging.error(f"Selenium fetch failed: {str(e)}")
            
            # If Selenium failed or wasn't available, try with requests
            if not job_details:
                logging.info("Attempting to fetch with requests as fallback")
                job_details = fetch_job_details_with_requests(job_link)
            
            if job_details:
                # Update job data with details
                job['jobAdDetails'] = job_details
                logging.info(f"Successfully fetched details for job {i+1}")
            else:
                # If both methods failed, add a placeholder
                job['jobAdDetails'] = "Failed to fetch job details. LinkedIn may require login."
                logging.warning(f"Failed to fetch details for job {i+1}")
            
            # Save after each fetch to avoid losing progress
            save_json_data(JSON_FILE_PATH, job_data)
            
            # Add random delay to avoid rate limiting
            delay = uniform(3.0, 7.0)  # More random delay
            logging.info(f"Waiting {delay:.2f} seconds before next request...")
            time.sleep(delay)
        
        logging.info("All jobs processed successfully!")
    
    finally:
        # Clean up Selenium driver
        if driver:
            try:
                driver.quit()
                logging.info("Selenium WebDriver closed successfully")
            except Exception as e:
                logging.error(f"Error closing Selenium WebDriver: {str(e)}")


if __name__ == "__main__":
    main()