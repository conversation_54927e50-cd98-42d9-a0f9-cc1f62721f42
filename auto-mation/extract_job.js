// 提取页面中所有工作卡片的信息
function extractJobCards() {
  // 获取所有包含工作卡片的元素
  const jobCards = document.querySelectorAll('div[data-search-sol-meta]');
  
  // 存储提取的信息
  const jobs = [];
  
  // 遍历每个工作卡片
  jobCards.forEach(card => {
      // 提取职位名称
      const jobTitle = card.querySelector('[data-automation="jobTitle"]')?.textContent || '';
      
      // 提取公司名称
      const jobAdvertiser = card.querySelector('[data-automation="jobAdvertiser"]')?.textContent || '';
      
      // 提取推荐职位链接
      const recommendedJobLink = card.querySelector('[data-automation^="recommendedJobLink"]')?.href || '';
      
      // 将提取的信息添加到数组
      jobs.push({
          jobTitle,
          jobAdvertiser,
          recommendedJobLink
      });
  });
  
  return jobs;
}

// 执行函数并输出结果
const jobData = extractJobCards();
console.log(jobData);