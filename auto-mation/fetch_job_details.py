import json
import requests
from bs4 import BeautifulSoup
import os
import re

import argparse
# Determine project root and JSON data path
root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
def get_json_path():
    parser = argparse.ArgumentParser()
    parser.add_argument('--jobdata', type=str, default=None, help='Job list JSON path')
    args, _ = parser.parse_known_args()
    if args.jobdata:
        # 确保相对路径转为绝对路径
        if not os.path.isabs(args.jobdata):
            return os.path.abspath(args.jobdata)
        return args.jobdata
    return os.path.join(root_dir, 'job-data', 'joblist_0512.json')
json_path = get_json_path()

# Common headers to mimic browser
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
                  'AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/135.0.0.0 Safari/537.36',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Referer': 'https://www.seek.com.au',
    'Origin': 'https://www.seek.com.au'
}

# Load job list
with open(json_path, 'r', encoding='utf-8') as f:
    jobs = json.load(f)

# Fetch and parse each job detail
for job in jobs:
    url = job.get('recommendedJobLink')
    try:
        resp = requests.get(url, headers=HEADERS, timeout=10)
        resp.raise_for_status()
    except Exception as e:
        print(f"Failed to fetch {url}: {e}")
        job['jobDetails'] = ''
        continue

    soup = BeautifulSoup(resp.text, 'html.parser')
    # Find the detail container by data-automation only
    detail_div = soup.find(attrs={ 'data-automation': 'jobAdDetails' })
    if detail_div:
        # extract inner text
        job['jobDetails'] = detail_div.get_text(separator='\n').strip()
    else:
        # fallback to JSON-LD description
        ld_script = soup.find('script', type='application/ld+json')
        if ld_script:
            try:
                ld = json.loads(ld_script.string)
                job['jobDetails'] = ld.get('description', '').strip()
            except:
                job['jobDetails'] = ''
        else:
            print(f"Detail section not found for {url}")
            job['jobDetails'] = ''

# Function to clean text of invalid unicode characters
def clean_unicode_text(text):
    if not isinstance(text, str):
        return text
    # 移除代理对字符 (surrogate pairs)
    return re.sub(r'[\ud800-\udfff]', '', text)

# Clean all job details before saving
for job in jobs:
    if 'jobDetails' in job and isinstance(job['jobDetails'], str):
        job['jobDetails'] = clean_unicode_text(job['jobDetails'])
    # 清理其他可能包含特殊字符的字段
    for field in ['jobTitle', 'jobAdvertiser']:
        if field in job and isinstance(job[field], str):
            job[field] = clean_unicode_text(job[field])

# Write updated data back to the same JSON file
with open(json_path, 'w', encoding='utf-8') as f:
    json.dump(jobs, f, ensure_ascii=False, indent=2)

print(f"Updated {len(jobs)} jobs with details.")
