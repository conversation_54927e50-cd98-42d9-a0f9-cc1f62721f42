import argparse
import os
import subprocess
import json

root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
scripts_dir = os.path.dirname(os.path.abspath(__file__))

def detect_job_source(jobdata_path):
    """检测工作数据中的主要来源，返回合适的抓取脚本"""
    try:
        with open(jobdata_path, 'r', encoding='utf-8') as f:
            jobs = json.load(f)
        
        if not jobs:
            return 'fetch_job_details.py'  # 默认
        
        # 统计不同网站的数量
        linkedin_count = 0
        seek_count = 0
        
        for job in jobs[:10]:  # 检查前10个作为样本
            url = job.get('recommendedJobLink', '')
            if 'linkedin.com' in url:
                linkedin_count += 1
            elif 'seek.com' in url:
                seek_count += 1
        
        # 根据主要来源选择脚本
        if linkedin_count > seek_count:
            return 'fetch_job_detail_linkedin.py'
        else:
            return 'fetch_job_details.py'
    
    except Exception as e:
        print(f"Error detecting job source: {e}")
        return 'fetch_job_details.py'  # 默认回退

def main():
    parser = argparse.ArgumentParser(description='Job Match 自动化主入口')
    parser.add_argument('--jobdata', type=str, default=os.path.join(root_dir, 'job-data', 'joblist_0512.json'), help='职位列表JSON路径')
    parser.add_argument('--force-script', type=str, choices=['linkedin', 'seek'], help='强制使用特定的抓取脚本')
    args = parser.parse_args()
    
    # 确保转换为绝对路径
    if not os.path.isabs(args.jobdata):
        jobdata_path = os.path.abspath(args.jobdata)
    else:
        jobdata_path = args.jobdata
        
    # 检查文件是否存在
    if not os.path.exists(jobdata_path):
        print(f"Error: Job data file not found: {jobdata_path}")
        return

    # 选择合适的抓取脚本
    if args.force_script:
        if args.force_script == 'linkedin':
            fetch_script = 'fetch_job_detail_linkedin.py'
        else:
            fetch_script = 'fetch_job_details.py'
        print(f"[1/3] 强制使用 {fetch_script} 抓取详情: {jobdata_path}")
    else:
        fetch_script = detect_job_source(jobdata_path)
        print(f"[1/3] 自动检测使用 {fetch_script} 抓取详情: {jobdata_path}")

    # 执行抓取脚本
    subprocess.run(['python3', os.path.join(scripts_dir, fetch_script), '--jobdata', jobdata_path], check=True)

    print(f"[2/3] 智能匹配分析: {jobdata_path}")
    # 捕获analyze_job_match.py的输出，获取生成的target_job文件路径
    process = subprocess.Popen(
        ['python3', os.path.join(scripts_dir, 'analyze_job_match.py'), '--jobdata', jobdata_path],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    
    target_job_path = None
    for line in process.stdout:
        print(line, end='')
        if line.startswith('TARGET_JOB_PATH='):
            target_job_path = line.strip().split('=', 1)[1]
    
    process.wait()
    if process.returncode != 0:
        raise subprocess.CalledProcessError(process.returncode, process.args)
    
    if target_job_path:
        print(f"[3/3] 批量生成Cover Letter (基于{os.path.basename(target_job_path)})")
        subprocess.run(['python3', os.path.join(scripts_dir, 'generate_coverletter.py'), '--target-job', target_job_path], check=True)
    else:
        print(f"[3/3] 批量生成Cover Letter (未找到target_job路径，使用默认文件)")
        subprocess.run(['python3', os.path.join(scripts_dir, 'generate_coverletter.py')], check=True)

if __name__ == '__main__':
    main()
