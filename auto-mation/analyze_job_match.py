import os
import json
import requests
import logging

# setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')

def load_json(path):
    with open(path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json(path, data):
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

import argparse
# paths
dir_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
def get_jobs_path():
    parser = argparse.ArgumentParser()
    parser.add_argument('--jobdata', type=str, default=None, help='Job list JSON path')
    args, _ = parser.parse_known_args()
    if args.jobdata:
        # 确保相对路径转为绝对路径
        if not os.path.isabs(args.jobdata):
            return os.path.abspath(args.jobdata)
        return args.jobdata
    return os.path.join(dir_root, 'job-data', 'job0504.json')

def get_target_path():
    # 生成带日期的target_job文件名
    from datetime import datetime
    today = datetime.now().strftime('%m%d')
    target_filename = f'target_job_{today}.json'
    return os.path.join(dir_root, 'job-data', target_filename)

path_jobs = get_jobs_path()
path_target = get_target_path()
path_cv = os.path.join(dir_root, 'personal_info', 'qiansui_cv.md')

# Load .env variables if file exists
dotenv_path = os.path.join(dir_root, '.env')
if os.path.exists(dotenv_path):
    with open(dotenv_path, 'r', encoding='utf-8') as df:
        for line in df:
            if line.strip() and not line.strip().startswith('#') and '=' in line:
                k, v = line.strip().split('=', 1)
                os.environ.setdefault(k, v)

# load data
jobs = load_json(path_jobs)
resume = open(path_cv, 'r', encoding='utf-8').read()
existing = []
if os.path.exists(path_target):
    # load existing matches if file is non-empty and valid JSON
    try:
        if os.path.getsize(path_target) > 0:
            existing = load_json(path_target)
    except (json.JSONDecodeError, OSError):
        existing = []

# prepare API
env_key = os.getenv('DEEPSEEK_API_KEY')
if not env_key:
    raise RuntimeError('DEEPSEEK_API_KEY not set. Please define it in .env or environment variables.')
headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {env_key}'
}
url = 'https://api.deepseek.com/chat/completions'

results = None
for job in jobs:
    prompt_sys = (
        f"You are a career matching assistant. Candidate resume:\n{resume}\n"
        "Evaluate the fit against the job description and output ONLY a JSON object with fields 'score' (integer 0-100) and 'evaluation' (≤200 characters summarizing key reasons)."
    )
    # Support both 'jobDetails' and 'jobAdDetails' field names
    job_description = job.get('jobDetails') or job.get('jobAdDetails', 'No job description available')
    prompt_user = f"Job description:\n{job_description}"
    body = {
        'model': 'deepseek-chat',
        'messages': [
            {'role': 'system', 'content': prompt_sys},
            {'role': 'user', 'content': prompt_user}
        ]
    }
    resp = requests.post(url, headers=headers, json=body, timeout=30)
    resp.raise_for_status()
    data = resp.json()
    # debug log full response
    logging.info(f"API response for {job['recommendedJobLink']}: {data}")
    # extract content from response: normalize data['output'] or top-level
    response_body = data.get('output', data)
    choices = response_body.get('choices', [])
    if not choices:
        logging.error(f"No choices in response for {job['recommendedJobLink']}: {data}")
        continue
    content = choices[0]['message']['content']
    # parse JSON response for score and evaluation
    try:
        result_json = json.loads(content)
        score = int(result_json.get('score', 0))
        evaluation = result_json.get('evaluation', '').strip()
    except Exception:
        # fallback: extract score from digits and preview evaluation
        import re
        m = re.search(r"(\d{1,3})", content)
        score = int(m.group(1)) if m else 0
        evaluation = content.strip().replace('\n', ' ')[:200]
    if score >= 80:
        entry = job.copy()
        entry['match_score'] = score
        entry['evaluation'] = evaluation
        # append immediately to target_job.json
        try:
            if os.path.exists(path_target) and os.path.getsize(path_target) > 0:
                existing_entries = load_json(path_target)
            else:
                existing_entries = []
        except:
            existing_entries = []
        existing_entries.append(entry)
        save_json(path_target, existing_entries)
        logging.info(f"Appended job '{job['jobTitle']}' with score {score} to {path_target}")

# 打印生成的target_job文件路径，供main.py获取
print(f"TARGET_JOB_PATH={path_target}")
