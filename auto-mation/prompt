# 提示词

## linkedin jobdetail
请基于 json 的数据格式，逐一获取linkedin链接页面内的工作详情，并返回每个工作详情的内容，以json 的格式返回。更新在现有的json 数据上。
- 请使用python 的 requests 库，获取页面内的工作详情。
- 解析内容为 class = "mt4" 的 内容数据，请参考sampledata,过程中注意移除不需要的html标签，只保留文本内容。
- 代码生成在


##  提取工作列表
 请基于前端页面的 html 代码，从页面中提取工作列表，并输出为 json 格式的数据。
- 注意，页面内的class每次会动态发生变化，因此在js 中，注意使用动态的方式，每次 class 的名称，方便每次变化后进行替换。
- class 一共 4个，我需要提取其中 3 个的属性：
  - card: 一个工作的卡片，包含了全部的信息，根据这个class进行逐个提取
  - jobTitle：岗位名称，即工作岗位的名称
  - jobAdvertiser：岗位 Advertiser，即招聘方
  - recommendedJobLink：推荐工作链接，即工作详情的链接
- 我需要提取的内容是：
  - jobTitle
  - jobAdvertiser
  - recommendedJobLink

接下来是本次的class 名称：
card class = "gg45di0 _1ubeeig93 _1ubeeig7z _1ubeeigbb _1ubeeiga7 _1ubeeig5f w39xvk4 w39xvk5 w39xvk6"

jobTitle class = "gg45di0"

jobAdvertiser class = "gg45di0 _1ubeeig4z _1oxsqkd0 _1oxsqkd1 _1oxsqkd22 _18ybopc4 _1oxsqkda"

recommendedJobLink class = "gg45di0 gg45dif  gg45di0 gg45dif w39xvk9 _1ubeeig5j _1ubeeigj _1ubeeigk _1ubeeigl _1ubeeigm _1ubeeig7"


example output:
```json
[
    {
        "jobTitle": "Product Manager - Consumer (Car Dealership & OEM Tyres)",
        "jobAdvertiser": "Tyremax Pty Ltd",
        "recommendedJobLink": "https://www.seek.com.au/job/83841733?ref=recom-homepage&pos=1#sol=b676ea5c3a136f795b025f6fe3bc4da4b4e9e2c6"
    },
]
```

## 获取工作详情
请基于 json 的数据格式，逐一获取页面内的工作详情，并返回每个工作详情的内容，以json 的格式返回。更新在现有的json 数据上。
- 请使用python 的 requests 库，获取页面内的工作详情。
- 解析内容为 class = "gg45di0 _1ubeeig5b _1ubeeighf _1ubeeig6z" 的 data-automation="jobAdDetails"  的标签。
- 代码请存放在 /auto-mation 文件夹下



## 分析工作
请基于json 的数据，逐一对工作详情与我简历的匹配度进行分析，针对匹配度高的工作，请将内容记录至json 文件中
- 使用 qwen-plus-latest  作为分析的模型，模型使用说明请参考文档
- 结果记录在 /job-data 目录下的 target_job.json 文件中, 使用update的方式,更新已有的json数据, 不要删除里面原有的数据
- 使用python进行代码实现，同时接口调用使用http
- 你需要动态得引入我们简历信息来构造你的系统prompt, 便于我简历更新后，你的代码可以继续使用
- 系统提示词的构造使用英文