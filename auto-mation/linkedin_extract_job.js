// /Users/<USER>/Desktop/aus_job/auto-mation/linkedin_extract_job.js
// This file extracts job information from LinkedIn job listings
// It is designed to extract jobTitle, jobAdvertiser, and recommendedJobLink from LinkedIn job cards

/**
 * Extracts job information from LinkedIn job listings
 * @returns {Array} Array of job objects with jobTitle, jobAdvertiser, and recommendedJobLink
 */
function extractJobCards() {
  // Select all job card containers in the LinkedIn job search results
  const jobCards = document.querySelectorAll('li.scaffold-layout__list-item');
  
  // Array to store extracted job information
  const jobs = [];
  
  // Process each job card
  jobCards.forEach(card => {
    // Extract job ID to verify we're working with a job card
    const jobId = card.getAttribute('data-occludable-job-id');
    if (!jobId) return; // Skip if not a valid job card
    
    // Find the job container within the list item
    const jobContainer = card.querySelector('div.job-card-container');
    if (!jobContainer) return;
    
    // Extract job title from the title link element
    const titleElement = jobContainer.querySelector('a.job-card-list__title--link');
    const jobTitle = titleElement ? 
      (titleElement.querySelector('strong') ? 
        titleElement.querySelector('strong').textContent.trim() : 
        (titleElement.getAttribute('aria-label') ? 
          titleElement.getAttribute('aria-label').replace(/ with verification$/i, '').trim() : 
          '')) : 
      '';
    
    // Extract company name from the subtitle element
    const companyElement = jobContainer.querySelector('.artdeco-entity-lockup__subtitle span');
    const jobAdvertiser = companyElement ? companyElement.textContent.trim() : '';
    
    // Extract job link from the title link element
    const recommendedJobLink = titleElement ? titleElement.href : '';
    
    // Add job information to the results array using the same field names as extract_job.js
    jobs.push({
      jobTitle,
      jobAdvertiser,
      recommendedJobLink
    });
  });
  
  return jobs;
}

// Execute the function and output the results
const jobData = extractJobCards();
console.log(jobData);

// Uncomment to display results in table format
// console.table(jobData);