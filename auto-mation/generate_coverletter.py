import os
import json
import requests


def load_file(path):
    with open(path, encoding="utf-8") as f:
        return f.read()


def main():
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--target-job', type=str, help='Target job JSON path')
    args, _ = parser.parse_known_args()
    
    # Paths
    script_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.abspath(os.path.join(script_dir, os.pardir))
    cv_path = os.path.join(root_dir, "personal_info", "qiansui_cv.md")
    prompt_path = os.path.join(script_dir, "coverletter-prompt.md")
    guide_path = os.path.join(root_dir, "doc", "How to write a career change cover letter (with examples).md")
    
    # 优先使用命令行参数指定的target_job路径
    if args.target_job:
        jobs_path = args.target_job
    else:
        # 如果未指定，查找最新的target_job文件
        import glob
        target_files = glob.glob(os.path.join(root_dir, "job-data", "target_job_*.json"))
        if target_files:
            # 按文件修改时间排序，取最新的
            jobs_path = max(target_files, key=os.path.getmtime)
        else:
            # 如果没有找到任何target_job文件，使用默认路径
            jobs_path = os.path.join(root_dir, "job-data", "target_job.json")
    
    print(f"Using target job file: {jobs_path}")
    output_dir = os.path.join(root_dir, "applying-job")
    os.makedirs(output_dir, exist_ok=True)

    # Load contents
    cv_content = load_file(cv_path)
    prompt_template = load_file(prompt_path)
    guide_content = load_file(guide_path)
    with open(jobs_path, encoding="utf-8") as f:
        jobs = json.load(f)

    # Load .env variables if file exists
    dotenv_path = os.path.join(root_dir, '.env')
    if os.path.exists(dotenv_path):
        with open(dotenv_path, 'r', encoding='utf-8') as df:
            for line in df:
                if line.strip() and not line.strip().startswith('#') and '=' in line:
                    k, v = line.strip().split('=', 1)
                    os.environ.setdefault(k, v)
                    
    # Init DeepSeek-compatible HTTP client
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        raise EnvironmentError("Please set DEEPSEEK_API_KEY in environment variables")
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    url = 'https://api.deepseek.com/chat/completions'

    for job in jobs:
        advertiser = job.get("jobAdvertiser", "company").replace("/", "_")
        filename = f"(saved){advertiser}.md"
        out_path = os.path.join(output_dir, filename)

        # Support both 'jobDetails' and 'jobAdDetails' field names
        job_details = job.get("jobDetails") or job.get("jobAdDetails", "")
        # Assemble prompt
        prompt = (f"# prompt_template:\n{prompt_template}\n\n"
                  f"# guide_content:\n{guide_content}\n\n"
                  f"# job_details:\n{job_details}\n\n"
                  f"# cv_content:\n{cv_content}")

        # Generate cover letter via HTTP
        body = {
            'model': 'deepseek-chat',
            'messages': [
                {'role': 'system', 'content': 'You are an expert in writing cover letters. Based on the given user information and job requirements, you need to write a cover letter that can help the user get an interview opportunity. The cover letter should be personalized and tailored to the user\'s strengths and the job requirements.'},
                {'role': 'user', 'content': prompt}
            ],
            'temperature': 0.7
        }
        resp = requests.post(url, headers=headers, json=body, timeout=30)
        resp.raise_for_status()
        data = resp.json()
        response_body = data.get('output', data)
        choices = response_body.get('choices', [])
        cover_text = choices[0]['message']['content'] if choices else ''

        # Write output file
        with open(out_path, "w", encoding="utf-8") as fout:
            fout.write(f"# {advertiser}\n\n")
            fout.write("statuts:\n- saved\n- waiting\n- interviewing\n- done\n\n")
            fout.write("## info\n")
            fout.write(json.dumps(job, ensure_ascii=False, indent=4))
            fout.write("\n\n## status:\n\n## coverletter:\n")
            fout.write(cover_text)
        print(f"Wrote cover letter to {out_path}")


if __name__ == "__main__":
    main()