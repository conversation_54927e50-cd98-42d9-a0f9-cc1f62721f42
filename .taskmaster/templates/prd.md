# Job Seeker - CLI 自动化工具

## 项目概述
将现有 `/auto-mation` 目录下的求职自动化功能整合为统一的 CLI 工具，提供友好的交互界面和模块化的功能选择。

## 功能需求

### 核心功能模块
1. **主流程执行 (main)** - 全自动化求职流程
2. **工作详情抓取 (fetch-job-detail)** - 通用网站抓取
3. **LinkedIn专项抓取 (fetch-job-detail-linkedin)** - LinkedIn专用抓取
4. **智能匹配分析 (analyze-job-match)** - AI驱动的工作匹配度分析
5. **求职信生成 (generate-coverletter)** - 自动化求职信生成

### 用户交互设计
```
┌─────────────────────────────────────┐
│        Job Seeker CLI v1.0          │
│     求职自动化工具集                  │
└─────────────────────────────────────┘

请选择功能：
[1] 🚀 完整自动化流程 (main)
[2] 📊 获取工作详情 (fetch-job-detail)  
[3] 💼 LinkedIn专项抓取 (fetch-job-detail-linkedin)
[4] 🎯 智能匹配分析 (analyze-job-match)
[5] 📝 生成求职信 (generate-coverletter)
[6] ⚙️  配置设置
[7] 📈 查看统计
[0] 退出

请输入选项 (0-7): 
```

### 数据文件管理
- 自动扫描 `/job-data` 目录下的 JSON 文件
- 提供文件选择界面，显示文件大小、修改时间
- 支持文件预览和验证

### 功能详细说明

#### 1. 完整自动化流程 (main)
- 自动检测数据源类型（LinkedIn vs Seek）
- 按顺序执行：抓取详情 → 智能匹配 → 生成求职信
- 实时显示进度和状态
- 支持断点续传和错误恢复

#### 2. 工作详情抓取模块
- **通用抓取**：支持 Seek.com.au 等求职网站
- **LinkedIn专项**：针对 LinkedIn 的优化抓取策略
- 智能重试机制和错误处理
- 支持批量处理和并发控制

#### 3. 智能匹配分析
- 基于 DeepSeek API 的 AI 分析
- 个人简历与职位描述的智能匹配
- 输出匹配度评分（0-100分）
- 生成详细的匹配分析报告

#### 4. 求职信生成
- 基于匹配分析结果自动生成个性化求职信
- 支持多种模板和风格
- 自动保存到 `/applying-job` 目录
- 包含职位信息和申请状态管理

## 技术方案选型

### 1. 核心框架选择

**推荐方案：Python + Click + Rich**

```python
# 技术栈组合
CLI框架: Click (简洁、强大的命令行框架)
界面美化: Rich (现代化的终端UI)
配置管理: Click + YAML/TOML
进度显示: Rich Progress
日志系统: 基于现有 logging 模块扩展
```

**优势**：
- Click：Python生态最成熟的CLI框架，与现有代码兼容性好
- Rich：提供现代化的终端界面，支持表格、进度条、颜色等
- 无需重写现有脚本，只需包装调用

**替代方案**：
- Typer (基于Click，类型提示友好)
- Fire (Google开源，自动生成CLI)
- Argparse (内置，但功能有限)

### 2. 项目结构设计

```
job_seeker_cli/
├── cli/
│   ├── __init__.py
│   ├── main.py              # CLI主入口
│   │   ├── __init__.py
│   │   ├── main_flow.py     # 主流程命令
│   │   ├── fetch_jobs.py    # 抓取相关命令
│   │   ├── analyze.py       # 分析命令
│   │   ├── generate.py      # 生成命令
│   │   └── config.py        # 配置命令
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── file_manager.py  # 文件管理工具
│   │   ├── ui_helpers.py    # UI辅助函数
│   │   └── validators.py    # 数据验证
│   └── config/
│       ├── __init__.py
│       ├── settings.py      # 配置管理
│       └── defaults.yaml    # 默认配置
├── automation/              # 重构后的自动化模块
│   ├── __init__.py
│   ├── main_processor.py    # 主流程处理器
│   ├── job_fetcher.py       # 统一的抓取接口
│   ├── job_analyzer.py      # 分析模块
│   └── letter_generator.py  # 求职信生成
├── setup.py
├── requirements.txt
└── README.md
```

### 3. 核心实现方案

#### 3.1 CLI主入口设计

```python
# cli/main.py
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Prompt, Confirm

@click.group()
@click.version_option()
def cli():
    """Job Seeker - 求职自动化工具集"""
    console = Console()
    
    # 显示欢迎界面
    welcome_panel = Panel.fit(
        "[bold blue]Job Seeker CLI v1.0[/bold blue]\n"
        "[italic]求职自动化工具集[/italic]",
        border_style="blue"
    )
    console.print(welcome_panel)

@cli.command()
def interactive():
    """交互式菜单模式"""
    console = Console()
    
    while True:
        # 显示菜单
        show_menu(console)
        choice = Prompt.ask("请输入选项", choices=["0","1","2","3","4","5","6","7"])
        
        if choice == "0":
            break
        elif choice == "1":
            run_main_flow()
        # ... 其他选项处理
```

#### 3.2 文件选择器实现

```python
# cli/utils/file_manager.py
from rich.table import Table
from rich.prompt import IntPrompt
import os
import glob
from datetime import datetime

def select_job_data_file():
    """交互式选择job-data文件"""
    console = Console()
    
    # 扫描job-data目录
    job_files = glob.glob("job-data/*.json")
    
    if not job_files:
        console.print("[red]未找到任何job数据文件[/red]")
        return None
    
    # 创建文件选择表格
    table = Table(title="可用的Job数据文件")
    table.add_column("序号", style="cyan", no_wrap=True)
    table.add_column("文件名", style="magenta")
    table.add_column("大小", style="green")
    table.add_column("修改时间", style="yellow")
    
    for i, file_path in enumerate(job_files, 1):
        stat = os.stat(file_path)
        size = f"{stat.st_size / 1024:.1f}KB"
        mtime = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")
        
        table.add_row(
            str(i),
            os.path.basename(file_path),
            size,
            mtime
        )
    
    console.print(table)
    
    choice = IntPrompt.ask(
        "请选择文件", 
        default=1, 
        choices=range(1, len(job_files) + 1)
    )
    
    return job_files[choice - 1]
```

#### 3.3 现有脚本包装器

```python
# automation/main_processor.py
import subprocess
import sys
from pathlib import Path
from rich.progress import Progress, SpinnerColumn, TextColumn

class MainFlowProcessor:
    def __init__(self, root_dir: Path):
        self.root_dir = root_dir
        self.scripts_dir = root_dir / "auto-mation"
    
    def run_full_process(self, job_data_path: str):
        """运行完整的自动化流程"""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # 步骤1: 抓取详情
            task1 = progress.add_task("抓取工作详情...", total=None)
            self._run_script("main.py", ["--jobdata", job_data_path])
            progress.remove_task(task1)
            
            # 步骤2: 分析匹配
            task2 = progress.add_task("分析工作匹配度...", total=None)
            target_job_path = self._run_analyze(job_data_path)
            progress.remove_task(task2)
            
            # 步骤3: 生成求职信
            task3 = progress.add_task("生成求职信...", total=None)
            self._run_script("generate_coverletter.py", ["--target-job", target_job_path])
            progress.remove_task(task3)
    
    def _run_script(self, script_name: str, args: list):
        """运行指定脚本"""
        cmd = [sys.executable, str(self.scripts_dir / script_name)] + args
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise RuntimeError(f"脚本执行失败: {result.stderr}")
        
        return result.stdout
```

### 4. 配置管理方案

```yaml
# cli/config/defaults.yaml
app:
  name: "Job Seeker CLI"
  version: "1.0.0"

paths:
  job_data_dir: "job-data"
  output_dir: "applying-job"
  automation_scripts: "auto-mation"

api:
  deepseek:
    timeout: 30
    max_retries: 3
  
defaults:
  fetch_method: "auto"  # auto, seek, linkedin
  match_threshold: 80
  
ui:
  theme: "blue"
  show_progress: true
  confirm_actions: true
```

### 5. 扩展功能设计

#### 5.1 统计功能
```python
@cli.command()
def stats():
    """查看求职统计"""
    console = Console()
    
    # 统计各种数据
    stats_data = collect_job_stats()
    
    # 显示统计表格
    table = Table(title="求职统计")
    table.add_column("指标", style="cyan")
    table.add_column("数值", style="magenta")
    
    table.add_row("总职位数", str(stats_data['total_jobs']))
    table.add_row("匹配职位数", str(stats_data['matched_jobs']))
    table.add_row("已生成求职信", str(stats_data['cover_letters']))
    table.add_row("匹配率", f"{stats_data['match_rate']:.1f}%")
    
    console.print(table)
```

#### 5.2 配置管理
```python
@cli.group()
def config():
    """配置管理"""
    pass

@config.command()
def show():
    """显示当前配置"""
    # 显示配置内容

@config.command()
@click.option('--api-key', help='设置API密钥')
def set(api_key):
    """设置配置项"""
    # 更新配置
```
