# Job Match 自动化工具

## 项目概述

"Job Match 自动化"是一套端到端的招聘信息采集与匹配分析工具，帮助求职者自动化筛选和分析职位，生成定制化求职信。主要功能包括：

1. **职位列表提取**：通过前端页面的 HTML，动态提取职位卡片信息（jobTitle、jobAdvertiser、recommendedJobLink）
2. **详情页抓取**：针对列表中的职位，获取详情页内容并解析核心字段（jobAdDetails）
3. **智能匹配分析**：调用大模型（如 qwen-plus-latest），基于用户简历与职位详情计算匹配度并打分
4. **自动生成求职信**：根据匹配结果，为高匹配度的职位自动生成定制化求职信

## 目录结构

```
aus_job/
├─ .env                       # 环境变量配置(API KEY 等)
├─ requirements.txt           # Python 依赖
├─ auto-mation/               # 核心自动化脚本
│   ├─ main.py                # 主程序入口
│   ├─ extract_job.js         # 动态提取职位列表
│   ├─ fetch_job_details.py   # 抓取并解析职位详情
│   ├─ analyze_job_match.py   # 基于简历和详情执行匹配打分
│   ├─ generate_coverletter.py # 生成求职信
│   └─ coverletter-prompt.md  # 求职信生成提示词
├─ chrome_extension/          # Chrome 浏览器插件
│   ├─ manifest.json          # 插件配置
│   ├─ popup.html/js          # 插件界面
│   ├─ content.js             # 内容脚本
│   └─ background.js          # 后台脚本
├─ job-data/                  # 职位数据存储
│   ├─ joblist_MMDD.json      # 职位列表（按日期命名）
│   └─ target_job_MMDD.json   # 匹配结果（按日期命名）
├─ personal_info/             # 用户简历或个人信息
│   └─ qiansui_cv.md          # 作为匹配输入的简历
├─ applying-job/              # 生成的求职信
│   ├─ (saved)Company.md      # 已保存的求职信
│   └─ (waiting)Company.md    # 待处理的求职信
└─ doc/                       # 项目文档
    └─ api.md                 # 接口及模型使用说明
```

## 使用方法

### 环境准备

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 配置 API 密钥：
   在项目根目录创建 `.env` 文件，添加：
   ```
   DASHSCOPE_API_KEY=your_api_key_here
   ```

### 主程序使用方法

主程序 `main.py` 是整个工作流的入口，可以一键执行从职位抓取到求职信生成的完整流程。

#### 基本用法

```bash
# 在项目根目录下运行
python auto-mation/main.py --jobdata job-data/joblist_0521.json

# 或在 auto-mation 目录下运行
cd auto-mation
python main.py --jobdata ../job-data/joblist_0521.json
```

#### 参数说明

- `--jobdata`：指定职位列表 JSON 文件路径（必选）

#### 执行流程

1. **抓取详情**：从 joblist 中获取每个职位的详细信息
2. **智能匹配**：分析职位与简历的匹配度，生成 target_job_MMDD.json
3. **生成求职信**：为高匹配度的职位自动生成求职信，保存到 applying-job 目录

### Chrome 插件使用方法

1. 打开 Chrome 浏览器，进入扩展管理页面 (chrome://extensions/)
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"，选择 `chrome_extension` 目录
4. 浏览职位网站，点击插件图标，一键提取当前页面的职位列表

### 单独使用各模块

也可以单独运行各个模块：

```bash
# 抓取职位详情
python auto-mation/fetch_job_details.py --jobdata job-data/joblist_0521.json

# 智能匹配分析
python auto-mation/analyze_job_match.py --jobdata job-data/joblist_0521.json

# 生成求职信（自动使用最新的 target_job 文件）
python auto-mation/generate_coverletter.py
```

## 工作流程

1. 使用 Chrome 插件或手动运行 extract_job.js 提取职位列表，保存为 joblist_MMDD.json
2. 运行 main.py 自动执行后续流程
3. 在 applying-job 目录查看生成的求职信
4. 根据需要编辑和发送求职信

## 注意事项

- 确保 .env 文件中配置了正确的 API 密钥
- 保持 personal_info/qiansui_cv.md 简历内容更新
- target_job 文件按日期自动命名，历史记录不会被覆盖

