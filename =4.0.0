Requirement already satisfied: selenium in ./.venv/lib/python3.9/site-packages (4.32.0)
Requirement already satisfied: webdriver-manager in ./.venv/lib/python3.9/site-packages (4.0.2)
Requirement already satisfied: trio~=0.17 in ./.venv/lib/python3.9/site-packages (from selenium) (0.30.0)
Requirement already satisfied: certifi>=2021.10.8 in ./.venv/lib/python3.9/site-packages (from selenium) (2025.4.26)
Requirement already satisfied: trio-websocket~=0.9 in ./.venv/lib/python3.9/site-packages (from selenium) (0.12.2)
Requirement already satisfied: typing_extensions~=4.9 in ./.venv/lib/python3.9/site-packages (from selenium) (4.13.2)
Requirement already satisfied: urllib3[socks]<3,>=1.26 in ./.venv/lib/python3.9/site-packages (from selenium) (2.4.0)
Requirement already satisfied: websocket-client~=1.8 in ./.venv/lib/python3.9/site-packages (from selenium) (1.8.0)
Requirement already satisfied: packaging in ./.venv/lib/python3.9/site-packages (from webdriver-manager) (25.0)
Requirement already satisfied: python-dotenv in ./.venv/lib/python3.9/site-packages (from webdriver-manager) (1.1.0)
Requirement already satisfied: requests in ./.venv/lib/python3.9/site-packages (from webdriver-manager) (2.32.3)
Requirement already satisfied: sortedcontainers in ./.venv/lib/python3.9/site-packages (from trio~=0.17->selenium) (2.4.0)
Requirement already satisfied: idna in ./.venv/lib/python3.9/site-packages (from trio~=0.17->selenium) (3.10)
Requirement already satisfied: exceptiongroup in ./.venv/lib/python3.9/site-packages (from trio~=0.17->selenium) (1.3.0)
Requirement already satisfied: sniffio>=1.3.0 in ./.venv/lib/python3.9/site-packages (from trio~=0.17->selenium) (1.3.1)
Requirement already satisfied: attrs>=23.2.0 in ./.venv/lib/python3.9/site-packages (from trio~=0.17->selenium) (25.3.0)
Requirement already satisfied: outcome in ./.venv/lib/python3.9/site-packages (from trio~=0.17->selenium) (1.3.0.post0)
Requirement already satisfied: wsproto>=0.14 in ./.venv/lib/python3.9/site-packages (from trio-websocket~=0.9->selenium) (1.2.0)
Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6 in ./.venv/lib/python3.9/site-packages (from urllib3[socks]<3,>=1.26->selenium) (1.7.1)
Requirement already satisfied: h11<1,>=0.9.0 in ./.venv/lib/python3.9/site-packages (from wsproto>=0.14->trio-websocket~=0.9->selenium) (0.16.0)
Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.9/site-packages (from requests->webdriver-manager) (3.4.2)
